# macOS Development Environment Cleaner

Bộ công cụ toàn diện để phân tích và dọn dẹp bộ nhớ cho môi trường phát triển macOS, hỗ trợ Java, Node.js, iOS, Android và các công cụ phát triển khác.

## Các file trong dự án

### 🧹 Công cụ dọn dẹp chính
1. **`macos_dev_cleaner.py`** - Script Python toàn diện cho môi trường phát triển
2. **`quick_dev_clean.sh`** - Script bash nhanh cho dọn dẹp hàng ngày
3. **`cleaner_config.json`** - File cấu hình tùy chỉnh

### 💿 Công cụ quét ổ đĩa tìm thư mục trùng lặp
4. **`disk_duplicate_scanner.py`** - Script Python quét toàn bộ ổ đĩa
5. **`quick_disk_scan.sh`** - <PERSON><PERSON>t bash quét nhanh ổ đĩa
6. **`content_duplicate_finder.py`** - T<PERSON><PERSON> thư mục trùng dựa trên nội dung

### 📁 Công cụ dọn thư mục trùng lặp (cục bộ)
7. **`clean-cache.sh`** - Script bash đơn giản để tìm thư mục trùng lặp
8. **`check-and-clean-duplicates.sh`** - Script bash nâng cao với tính năng dọn dẹp
9. **`duplicate_cleaner.py`** - Script Python với nhiều tùy chọn và tiêu chí

## Cách sử dụng

## 🍎 Dọn dẹp môi trường phát triển macOS

### 1. Script Python toàn diện (`macos_dev_cleaner.py`)

#### Cấp quyền thực thi:
```bash
chmod +x macos_dev_cleaner.py
```

#### Các lệnh cơ bản:

**Phân tích hệ thống (không xóa gì):**
```bash
python3 macos_dev_cleaner.py
```

**Dọn dẹp thực sự:**
```bash
python3 macos_dev_cleaner.py --execute
```

**Dọn dẹp aggressive (bao gồm project cũ):**
```bash
python3 macos_dev_cleaner.py --execute --aggressive
```

**Chỉ dọn cache (không động build artifacts):**
```bash
python3 macos_dev_cleaner.py --execute --cache-only
```

**Không chạy system cleanup:**
```bash
python3 macos_dev_cleaner.py --execute --no-system
```

#### Tính năng:
- ✅ Phân tích toàn diện cache và build artifacts
- ✅ Kiểm tra process đang chạy để tránh xung đột
- ✅ Phát hiện project đang active (git activity, file mới)
- ✅ Dọn dẹp cache của Xcode, Android Studio, IntelliJ
- ✅ Dọn dẹp npm, yarn, gradle, maven cache
- ✅ Tìm và xóa build artifacts (node_modules, target, build, etc.)
- ✅ System cleanup (purge memory, DNS cache, etc.)
- ✅ Báo cáo chi tiết dung lượng tiết kiệm

### 2. Script Bash nhanh (`quick_dev_clean.sh`)

#### Cấp quyền thực thi:
```bash
chmod +x quick_dev_clean.sh
```

#### Các lệnh cơ bản:

**Phân tích nhanh:**
```bash
./quick_dev_clean.sh
```

**Dọn dẹp nhanh:**
```bash
./quick_dev_clean.sh --execute
```

**Dọn dẹp aggressive:**
```bash
./quick_dev_clean.sh --execute --aggressive
```

**Chỉ dọn cache:**
```bash
./quick_dev_clean.sh --execute --cache-only
```

#### Tính năng:
- ✅ Dọn dẹp nhanh các cache phổ biến
- ✅ Kiểm tra process đang chạy
- ✅ Tìm build artifacts trong project folders
- ✅ Dọn temp files và log files
- ✅ System cleanup cơ bản

## 💿 Quét ổ đĩa tìm thư mục trùng lặp

### 3. Script Python quét toàn diện (`disk_duplicate_scanner.py`)

#### Cấp quyền thực thi:
```bash
chmod +x disk_duplicate_scanner.py
```

#### Các lệnh cơ bản:

**Quét mặc định (Users, Applications, opt, usr/local):**
```bash
python3 disk_duplicate_scanner.py
```

**Quét đường dẫn cụ thể:**
```bash
python3 disk_duplicate_scanner.py --paths /Users /Applications
```

**Phân tích sâu (so sánh nội dung):**
```bash
python3 disk_duplicate_scanner.py --deep-analysis
```

**Xuất kết quả ra file:**
```bash
python3 disk_duplicate_scanner.py --output duplicates.json
```

**Tùy chỉnh kích thước và độ sâu:**
```bash
python3 disk_duplicate_scanner.py --min-size 10 --max-depth 5
```

#### Tính năng:
- ✅ Quét toàn bộ ổ đĩa một cách hiệu quả
- ✅ Loại trừ thư mục hệ thống nguy hiểm
- ✅ Nhóm thư mục theo tên và kích thước
- ✅ Phân tích sâu bằng so sánh nội dung (tùy chọn)
- ✅ Hiển thị tiến trình real-time
- ✅ Xuất báo cáo JSON chi tiết
- ✅ Tính toán dung lượng có thể tiết kiệm

### 4. Script Bash quét nhanh (`quick_disk_scan.sh`)

#### Cấp quyền thực thi:
```bash
chmod +x quick_disk_scan.sh
```

#### Các lệnh cơ bản:

**Quét nhanh mặc định:**
```bash
./quick_disk_scan.sh
```

**Quét với chi tiết đường dẫn:**
```bash
./quick_disk_scan.sh --details
```

**Quét thư mục cụ thể:**
```bash
./quick_disk_scan.sh --paths /Users,/Applications
```

**Lưu kết quả ra file:**
```bash
./quick_disk_scan.sh --output duplicates.txt --details
```

#### Tính năng:
- ✅ Quét nhanh với bash thuần
- ✅ Hiển thị thống kê tổng quan
- ✅ Tùy chọn hiển thị chi tiết đường dẫn
- ✅ Xuất kết quả ra file text
- ✅ Tính toán dung lượng tiết kiệm

### 5. Tìm thư mục trùng theo nội dung (`content_duplicate_finder.py`)

#### Cấp quyền thực thi:
```bash
chmod +x content_duplicate_finder.py
```

#### Các lệnh cơ bản:

**Quét và so sánh nội dung (quick mode):**
```bash
python3 content_duplicate_finder.py /Users/<USER>/Documents
```

**Phân tích chính xác (full hash):**
```bash
python3 content_duplicate_finder.py /Users/<USER>/Documents --full-hash
```

**Đề xuất hành động dọn dẹp:**
```bash
python3 content_duplicate_finder.py /Users/<USER>/Documents --suggest-cleanup
```

**Sử dụng thuật toán hash khác:**
```bash
python3 content_duplicate_finder.py /Users/<USER>/Documents --algorithm sha256
```

#### Tính năng:
- ✅ So sánh nội dung thực tế của thư mục
- ✅ Độ tin cậy cao (100% giống nhau)
- ✅ Hỗ trợ nhiều thuật toán hash (MD5, SHA1, SHA256)
- ✅ Quick mode và Full mode
- ✅ Đề xuất hành động dọn dẹp cụ thể
- ✅ Xuất báo cáo JSON chi tiết

## 📁 Dọn dẹp thư mục trùng lặp (cục bộ)

### 6. Script Bash nâng cao (`check-and-clean-duplicates.sh`)

#### Cấp quyền thực thi:
```bash
chmod +x check-and-clean-duplicates.sh
```

#### Các lệnh cơ bản:

**Chỉ kiểm tra (không xóa):**
```bash
./check-and-clean-duplicates.sh
```

**Kiểm tra và dọn dẹp tự động:**
```bash
./check-and-clean-duplicates.sh --clean
```

**Tìm kiếm trong thư mục khác:**
```bash
./check-and-clean-duplicates.sh --path /Users/<USER>/Desktop/
```

**Dọn dẹp không hỏi xác nhận:**
```bash
./check-and-clean-duplicates.sh --clean --non-interactive
```

#### Tham số:
- `--clean`: Tự động xóa thư mục trùng (giữ lại thư mục có kích thước lớn nhất)
- `--path PATH`: Đường dẫn để tìm kiếm
- `--non-interactive`: Không hỏi xác nhận khi xóa
- `--help`: Hiển thị hướng dẫn

### 7. Script Python (`duplicate_cleaner.py`)

#### Cấp quyền thực thi:
```bash
chmod +x duplicate_cleaner.py
```

#### Các lệnh cơ bản:

**Chỉ kiểm tra (dry run):**
```bash
python3 duplicate_cleaner.py
```

**Thực sự xóa:**
```bash
python3 duplicate_cleaner.py --execute
```

**Tìm kiếm trong thư mục khác:**
```bash
python3 duplicate_cleaner.py /Users/<USER>/Desktop/
```

**Chọn tiêu chí giữ lại thư mục:**
```bash
# Giữ thư mục lớn nhất (mặc định)
python3 duplicate_cleaner.py --keep largest

# Giữ thư mục mới nhất
python3 duplicate_cleaner.py --keep newest

# Giữ thư mục cũ nhất
python3 duplicate_cleaner.py --keep oldest

# Giữ thư mục đầu tiên tìm thấy
python3 duplicate_cleaner.py --keep first_found
```

**Dọn dẹp không hỏi xác nhận:**
```bash
python3 duplicate_cleaner.py --execute --non-interactive
```

#### Tham số:
- `--execute`: Thực sự xóa thư mục (mặc định chỉ hiển thị)
- `--keep {largest,newest,oldest,first_found}`: Tiêu chí chọn thư mục để giữ lại
- `--non-interactive`: Không hỏi xác nhận khi xóa

## Tính năng

### Script Bash (`check-and-clean-duplicates.sh`)
- ✅ Tìm kiếm thư mục trùng lặp theo tên
- ✅ Hiển thị kích thước từng thư mục
- ✅ Tự động giữ lại thư mục có kích thước lớn nhất
- ✅ Chế độ xác nhận tương tác
- ✅ Màu sắc trong terminal
- ✅ Kiểm tra đường dẫn hợp lệ

### Script Python (`duplicate_cleaner.py`)
- ✅ Tìm kiếm thư mục trùng lặp theo tên
- ✅ Nhiều tiêu chí để chọn thư mục giữ lại:
  - Kích thước lớn nhất
  - Thời gian sửa đổi mới nhất
  - Thời gian sửa đổi cũ nhất
  - Thư mục đầu tiên tìm thấy
- ✅ Hiển thị thông tin chi tiết (kích thước, thời gian)
- ✅ Chế độ dry-run an toàn
- ✅ Tính toán dung lượng tiết kiệm được
- ✅ Xử lý lỗi tốt hơn

## 🚀 Quy trình sử dụng khuyến nghị

### Dọn dẹp hàng ngày (5 phút):
```bash
# 1. Phân tích nhanh
./quick_dev_clean.sh

# 2. Dọn dẹp cache an toàn
./quick_dev_clean.sh --execute --cache-only
```

### Dọn dẹp hàng tuần (15 phút):
```bash
# 1. Phân tích toàn diện
python3 macos_dev_cleaner.py

# 2. Dọn dẹp moderate
python3 macos_dev_cleaner.py --execute

# 3. Quét ổ đĩa tìm thư mục trùng lặp
python3 disk_duplicate_scanner.py --min-size 5

# 4. Dọn thư mục trùng lặp cục bộ
python3 duplicate_cleaner.py /Users/<USER>/Documents/ --execute
```

### Dọn dẹp sâu hàng tháng (30 phút):
```bash
# 1. Backup dữ liệu quan trọng

# 2. Quét toàn bộ ổ đĩa với phân tích sâu
python3 disk_duplicate_scanner.py --deep-analysis --output monthly_scan.json

# 3. Tìm thư mục trùng theo nội dung
python3 content_duplicate_finder.py /Users /Applications --suggest-cleanup

# 4. Dọn dẹp aggressive
python3 macos_dev_cleaner.py --execute --aggressive

# 5. Dọn thư mục trùng lặp toàn bộ
./check-and-clean-duplicates.sh --clean --path /Users/<USER>/

# 6. Kiểm tra lại
python3 macos_dev_cleaner.py
```

## ⚠️ Lưu ý an toàn

### Quan trọng:
- **Luôn chạy chế độ phân tích trước** khi thực sự xóa
- **Backup dữ liệu quan trọng** trước khi dọn dẹp
- Script tự động **kiểm tra process đang chạy** để tránh xung đột
- **Không xóa project đang active** (có git activity gần đây)
- **Kiểm tra kỹ danh sách** trước khi xác nhận xóa

### Các thư mục được bảo vệ:
- `~/Library/Application Support` (trừ cache)
- `~/Library/Preferences`
- `~/Library/Keychains`
- `~/.ssh`
- `~/.gnupg`
- Project có git activity trong 7 ngày

### Process được kiểm tra:
- Xcode, Android Studio, IntelliJ IDEA
- npm, yarn, gradle, maven
- Docker, Simulator

## 📋 Các loại file/folder được dọn dẹp

### Cache Folders:
- **Xcode**: DerivedData, Archives, Device Support
- **Android**: .android/cache, .gradle/caches
- **Node.js**: .npm/_cacache, .yarn/cache, .pnpm-store
- **Java**: .m2/repository, .gradle/caches, JetBrains cache
- **System**: ~/Library/Caches, ~/Library/Logs, /tmp

### Build Artifacts:
- `node_modules`, `build`, `dist`, `target`
- `.gradle`, `DerivedData`, `.next`, `.nuxt`
- `coverage`, `__pycache__`, `.pytest_cache`
- `Pods`, `Carthage/Build`

### Temp Files:
- `.log`, `.tmp`, `.cache`, `.bak`, `.swp`
- `.DS_Store`, `Thumbs.db`, `.pid`

## 🛠️ Yêu cầu hệ thống

- **macOS** 10.14+ (Mojave trở lên)
- **Python 3.6+** (không cần thư viện bổ sung)
- **Bash 4.0+**
- **bc** command (cài đặt: `brew install bc`)
- **sudo** access cho system cleanup

## Troubleshooting

**Lỗi permission denied:**
```bash
chmod +x check-and-clean-duplicates.sh
chmod +x duplicate_cleaner.py
```

**Python không tìm thấy:**
```bash
# Thử với python thay vì python3
python duplicate_cleaner.py
```
