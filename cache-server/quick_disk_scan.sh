#!/bin/bash

# Quick Disk Duplicate Scanner for macOS
# Quét nhanh ổ đĩa tìm thư mục trùng lặp

# Màu sắc
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Biến mặc định
SCAN_PATHS=("/Users" "/Applications" "/opt" "/usr/local")
EXCLUDE_PATHS=("/System" "/Library/System" "/private/var" "/dev" "/proc" "/tmp")
MIN_SIZE_MB=1
MAX_DEPTH=8
OUTPUT_FILE=""
SHOW_DETAILS=false

# Hàm hiển thị help
show_help() {
    echo "💿 Quick Disk Duplicate Scanner for macOS"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "OPTIONS:"
    echo "  --paths PATH1,PATH2,...    Đường dẫn để quét (mặc định: /Users,/Applications,/opt,/usr/local)"
    echo "  --exclude PATH1,PATH2,...  Đường dẫn loại trừ"
    echo "  --min-size SIZE_MB         Kích thước tối thiểu (MB) (mặc định: 1)"
    echo "  --max-depth DEPTH          Độ sâu tối đa (mặc định: 8)"
    echo "  --output FILE              File để lưu kết quả"
    echo "  --details                  Hiển thị chi tiết đường dẫn"
    echo "  --help                     Hiển thị help này"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Quét mặc định"
    echo "  $0 --paths /Users,/Applications      # Quét thư mục cụ thể"
    echo "  $0 --min-size 10 --details          # Quét folder >= 10MB với chi tiết"
    echo "  $0 --output duplicates.txt           # Lưu kết quả ra file"
}

# Hàm format kích thước
format_size() {
    local size_kb=$1
    if [ $size_kb -gt 1048576 ]; then
        echo "$(echo "scale=1; $size_kb/1048576" | bc)GB"
    elif [ $size_kb -gt 1024 ]; then
        echo "$(echo "scale=1; $size_kb/1024" | bc)MB"
    else
        echo "${size_kb}KB"
    fi
}

# Hàm kiểm tra xem đường dẫn có bị loại trừ không
is_excluded() {
    local path="$1"
    for exclude in "${EXCLUDE_PATHS[@]}"; do
        if [[ "$path" == "$exclude"* ]]; then
            return 0
        fi
    done
    return 1
}

# Hàm tính kích thước thư mục
get_folder_size() {
    local folder="$1"
    if [ -d "$folder" ]; then
        du -sk "$folder" 2>/dev/null | cut -f1
    else
        echo "0"
    fi
}

# Hàm quét thư mục
scan_folders() {
    local scan_path="$1"
    local current_depth="$2"
    local temp_file="$3"
    
    if [ $current_depth -gt $MAX_DEPTH ]; then
        return
    fi
    
    if is_excluded "$scan_path"; then
        return
    fi
    
    if [ ! -d "$scan_path" ] || [ ! -r "$scan_path" ]; then
        return
    fi
    
    # Quét thư mục hiện tại
    find "$scan_path" -maxdepth 1 -mindepth 1 -type d 2>/dev/null | while read -r folder; do
        # Bỏ qua thư mục bị loại trừ
        if is_excluded "$folder"; then
            continue
        fi
        
        # Tính kích thước
        local size=$(get_folder_size "$folder")
        local min_size_kb=$((MIN_SIZE_MB * 1024))
        
        if [ $size -ge $min_size_kb ]; then
            local folder_name=$(basename "$folder")
            local mtime=$(stat -f %m "$folder" 2>/dev/null || echo "0")
            
            # Ghi vào file tạm: tên_thư_mục|đường_dẫn|kích_thước|thời_gian
            echo "${folder_name}|${folder}|${size}|${mtime}" >> "$temp_file"
        fi
        
        # Quét đệ quy nếu chưa đạt độ sâu tối đa
        if [ $current_depth -lt $MAX_DEPTH ]; then
            scan_folders "$folder" $((current_depth + 1)) "$temp_file"
        fi
    done
}

# Hàm phân tích thư mục trùng lặp
analyze_duplicates() {
    local temp_file="$1"
    local result_file="$2"
    
    echo -e "\n🔬 ${BLUE}Phân tích thư mục trùng lặp...${NC}"
    
    # Sắp xếp theo tên thư mục
    sort "$temp_file" > "${temp_file}.sorted"
    
    # Tìm thư mục trùng lặp
    local current_name=""
    local current_group=()
    local total_groups=0
    local total_folders=0
    local total_size_saved=0
    
    while IFS='|' read -r name path size mtime; do
        if [ "$name" != "$current_name" ]; then
            # Xử lý nhóm trước đó
            if [ ${#current_group[@]} -gt 1 ]; then
                process_duplicate_group "$current_name" current_group[@] "$result_file"
                total_groups=$((total_groups + 1))
                total_folders=$((total_folders + ${#current_group[@]}))
                
                # Tính dung lượng có thể tiết kiệm (giữ 1, xóa các folder còn lại)
                local group_size=$(echo "${current_group[0]}" | cut -d'|' -f3)
                local saved_size=$((group_size * (${#current_group[@]} - 1)))
                total_size_saved=$((total_size_saved + saved_size))
            fi
            
            # Bắt đầu nhóm mới
            current_name="$name"
            current_group=("$name|$path|$size|$mtime")
        else
            # Thêm vào nhóm hiện tại
            current_group+=("$name|$path|$size|$mtime")
        fi
    done < "${temp_file}.sorted"
    
    # Xử lý nhóm cuối cùng
    if [ ${#current_group[@]} -gt 1 ]; then
        process_duplicate_group "$current_name" current_group[@] "$result_file"
        total_groups=$((total_groups + 1))
        total_folders=$((total_folders + ${#current_group[@]}))
        
        local group_size=$(echo "${current_group[0]}" | cut -d'|' -f3)
        local saved_size=$((group_size * (${#current_group[@]} - 1)))
        total_size_saved=$((total_size_saved + saved_size))
    fi
    
    # Hiển thị thống kê
    echo -e "\n${'='*60}"
    echo -e "${GREEN}📊 THỐNG KÊ TỔNG QUAN${NC}"
    echo -e "${'='*60}"
    echo -e "${BLUE}🔍 Nhóm trùng lặp tìm thấy:${NC} $total_groups"
    echo -e "${BLUE}📁 Tổng thư mục trùng:${NC} $total_folders"
    echo -e "${BLUE}💾 Dung lượng có thể tiết kiệm:${NC} $(format_size $total_size_saved)"
    
    rm -f "${temp_file}.sorted"
}

# Hàm xử lý một nhóm thư mục trùng lặp
process_duplicate_group() {
    local group_name="$1"
    shift
    local group_items=("$@")
    local result_file="$3"
    
    # Nhóm theo kích thước
    declare -A size_groups
    for item in "${group_items[@]}"; do
        local size=$(echo "$item" | cut -d'|' -f3)
        if [ -z "${size_groups[$size]}" ]; then
            size_groups[$size]="$item"
        else
            size_groups[$size]="${size_groups[$size]}
$item"
        fi
    done
    
    # Chỉ hiển thị nhóm có > 1 thư mục cùng kích thước
    for size in "${!size_groups[@]}"; do
        local count=$(echo "${size_groups[$size]}" | wc -l)
        if [ $count -gt 1 ]; then
            echo -e "\n📁 ${YELLOW}Thư mục trùng:${NC} ${BLUE}$group_name${NC}"
            echo -e "📏 ${YELLOW}Kích thước:${NC} $(format_size $size)"
            echo -e "📊 ${YELLOW}Số lượng:${NC} $count thư mục"
            
            if [ "$SHOW_DETAILS" = true ]; then
                echo -e "📍 ${YELLOW}Đường dẫn:${NC}"
                echo "${size_groups[$size]}" | while IFS='|' read -r name path size mtime; do
                    local date_str=$(date -r $mtime '+%Y-%m-%d %H:%M' 2>/dev/null || echo "Unknown")
                    echo -e "   • $path ${PURPLE}(sửa đổi: $date_str)${NC}"
                done
            fi
            
            # Ghi vào file nếu có
            if [ -n "$result_file" ]; then
                echo "=== $group_name ($(format_size $size)) ===" >> "$result_file"
                echo "${size_groups[$size]}" | while IFS='|' read -r name path size mtime; do
                    echo "$path" >> "$result_file"
                done
                echo "" >> "$result_file"
            fi
            
            # Tính dung lượng có thể tiết kiệm
            local potential_saving=$((size * (count - 1)))
            echo -e "💰 ${GREEN}Có thể tiết kiệm:${NC} $(format_size $potential_saving)"
            echo -e "${PURPLE}${'─'*50}${NC}"
        fi
    done
}

# Hàm main
main() {
    echo -e "${BLUE}💿 Quick Disk Duplicate Scanner for macOS${NC}"
    echo "=================================================="
    
    # Tạo file tạm
    local temp_file=$(mktemp)
    local scan_start=$(date +%s)
    
    echo -e "${BLUE}📂 Đường dẫn quét:${NC} ${SCAN_PATHS[*]}"
    echo -e "${BLUE}🚫 Loại trừ:${NC} ${EXCLUDE_PATHS[*]:0:3}..."
    echo -e "${BLUE}📏 Kích thước tối thiểu:${NC} ${MIN_SIZE_MB}MB"
    echo -e "${BLUE}📊 Độ sâu tối đa:${NC} $MAX_DEPTH"
    echo ""
    
    # Quét từng đường dẫn
    local total_scanned=0
    for scan_path in "${SCAN_PATHS[@]}"; do
        if [ -d "$scan_path" ]; then
            echo -e "🔍 ${BLUE}Đang quét:${NC} $scan_path"
            scan_folders "$scan_path" 0 "$temp_file"
            local current_count=$(wc -l < "$temp_file")
            echo -e "   📁 Tìm thấy: $((current_count - total_scanned)) thư mục"
            total_scanned=$current_count
        else
            echo -e "⚠️  ${YELLOW}Bỏ qua:${NC} $scan_path (không tồn tại)"
        fi
    done
    
    local scan_end=$(date +%s)
    local scan_duration=$((scan_end - scan_start))
    
    echo -e "\n✅ ${GREEN}Hoàn thành quét!${NC}"
    echo -e "⏱️  ${BLUE}Thời gian:${NC} ${scan_duration}s"
    echo -e "📁 ${BLUE}Tổng thư mục:${NC} $total_scanned"
    
    if [ $total_scanned -eq 0 ]; then
        echo -e "${YELLOW}⚠️  Không tìm thấy thư mục nào phù hợp điều kiện${NC}"
        rm -f "$temp_file"
        return
    fi
    
    # Phân tích
    analyze_duplicates "$temp_file" "$OUTPUT_FILE"
    
    # Dọn dẹp
    rm -f "$temp_file"
    
    if [ -n "$OUTPUT_FILE" ]; then
        echo -e "\n💾 ${GREEN}Đã lưu kết quả vào:${NC} $OUTPUT_FILE"
    fi
    
    echo -e "\n${BLUE}💡 ĐỀ XUẤT:${NC}"
    echo -e "   • Kiểm tra kỹ từng nhóm trước khi xóa"
    echo -e "   • Backup dữ liệu quan trọng trước khi xóa"
    echo -e "   • Sử dụng --details để xem đường dẫn chi tiết"
    echo -e "   • Sử dụng disk_duplicate_scanner.py để phân tích sâu hơn"
}

# Xử lý tham số dòng lệnh
while [[ $# -gt 0 ]]; do
    case $1 in
        --paths)
            IFS=',' read -ra SCAN_PATHS <<< "$2"
            shift 2
            ;;
        --exclude)
            IFS=',' read -ra EXCLUDE_PATHS <<< "$2"
            shift 2
            ;;
        --min-size)
            MIN_SIZE_MB="$2"
            shift 2
            ;;
        --max-depth)
            MAX_DEPTH="$2"
            shift 2
            ;;
        --output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        --details)
            SHOW_DETAILS=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "Tham số không hợp lệ: $1"
            show_help
            exit 1
            ;;
    esac
done

# Kiểm tra bc command
if ! command -v bc &> /dev/null; then
    echo -e "${RED}❌ Lệnh 'bc' không tìm thấy. Cài đặt: brew install bc${NC}"
    exit 1
fi

# Chạy main
main
