#!/bin/bash

# Quick Development Environment Cleaner for macOS
# Dọn dẹp nhanh môi trường phát triển mà không ảnh hưởng đến công việc hiện tại

# Màu sắc
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Biến
DRY_RUN=true
AGGRESSIVE=false
TOTAL_SAVED=0

# Hàm hiển thị help
show_help() {
    echo "🍎 Quick macOS Development Environment Cleaner"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "OPTIONS:"
    echo "  --execute        Thực sự xóa files (mặc định chỉ phân tích)"
    echo "  --aggressive     Dọn dẹp aggressive hơn"
    echo "  --cache-only     Chỉ dọn cache, không động build artifacts"
    echo "  --help           Hiển thị help này"
    echo ""
    echo "Examples:"
    echo "  $0                    # Phân tích và hiển thị"
    echo "  $0 --execute         # Thực sự dọn dẹp"
    echo "  $0 --cache-only      # Chỉ dọn cache"
}

# Hàm tính kích thước thư mục
get_folder_size() {
    local folder="$1"
    if [ -d "$folder" ]; then
        du -sk "$folder" 2>/dev/null | cut -f1
    else
        echo "0"
    fi
}

# Hàm format kích thước
format_size() {
    local size_kb=$1
    if [ $size_kb -gt 1048576 ]; then
        echo "$(echo "scale=1; $size_kb/1048576" | bc)GB"
    elif [ $size_kb -gt 1024 ]; then
        echo "$(echo "scale=1; $size_kb/1024" | bc)MB"
    else
        echo "${size_kb}KB"
    fi
}

# Hàm kiểm tra xem process có đang chạy không
is_process_running() {
    local process_name="$1"
    pgrep -f "$process_name" > /dev/null 2>&1
}

# Hàm dọn dẹp cache an toàn
clean_cache_folder() {
    local name="$1"
    local path="$2"
    local check_process="$3"
    
    if [ ! -d "$path" ]; then
        return
    fi
    
    local size=$(get_folder_size "$path")
    if [ $size -eq 0 ]; then
        return
    fi
    
    echo -e "  📁 ${BLUE}$name${NC}: $path"
    echo -e "     📏 Kích thước: ${YELLOW}$(format_size $size)${NC}"
    
    # Kiểm tra process đang chạy
    if [ -n "$check_process" ] && is_process_running "$check_process"; then
        echo -e "     ⚠️  ${YELLOW}Bỏ qua (process $check_process đang chạy)${NC}"
        return
    fi
    
    if [ "$DRY_RUN" = false ]; then
        # Xóa nội dung thư mục nhưng giữ lại thư mục gốc
        if rm -rf "$path"/* 2>/dev/null; then
            echo -e "     ✅ ${GREEN}Đã dọn dẹp${NC}"
            TOTAL_SAVED=$((TOTAL_SAVED + size))
        else
            echo -e "     ❌ ${RED}Lỗi khi dọn dẹp${NC}"
        fi
    else
        echo -e "     🔍 ${PURPLE}[DRY RUN] Sẽ dọn dẹp${NC}"
        TOTAL_SAVED=$((TOTAL_SAVED + size))
    fi
}

# Hàm tìm và dọn build artifacts
clean_build_artifacts() {
    echo -e "\n🔨 ${BLUE}Dọn dẹp build artifacts...${NC}"
    
    local search_dirs=(
        "$HOME/Documents"
        "$HOME/Desktop"
        "$HOME/Projects"
        "$HOME/Development"
    )
    
    local artifacts=(
        "node_modules"
        "build"
        "dist"
        "target"
        ".gradle"
        "DerivedData"
        ".next"
        ".nuxt"
        "coverage"
        "__pycache__"
    )
    
    for search_dir in "${search_dirs[@]}"; do
        if [ ! -d "$search_dir" ]; then
            continue
        fi
        
        echo -e "  📂 Quét: ${BLUE}$search_dir${NC}"
        
        for artifact in "${artifacts[@]}"; do
            while IFS= read -r -d '' artifact_path; do
                # Kiểm tra xem có phải project active không (có git activity gần đây)
                local project_dir=$(dirname "$artifact_path")
                local git_log="$project_dir/.git/logs/HEAD"
                
                if [ -f "$git_log" ]; then
                    local last_commit=$(stat -f %m "$git_log" 2>/dev/null || echo "0")
                    local current_time=$(date +%s)
                    local days_ago=$(( (current_time - last_commit) / 86400 ))
                    
                    if [ $days_ago -lt 7 ] && [ "$AGGRESSIVE" = false ]; then
                        continue  # Bỏ qua project active gần đây
                    fi
                fi
                
                local size=$(get_folder_size "$artifact_path")
                if [ $size -gt 1024 ]; then  # Chỉ quan tâm > 1MB
                    echo -e "    📁 $artifact_path ($(format_size $size))"
                    
                    if [ "$DRY_RUN" = false ]; then
                        if rm -rf "$artifact_path" 2>/dev/null; then
                            echo -e "       ✅ ${GREEN}Đã xóa${NC}"
                            TOTAL_SAVED=$((TOTAL_SAVED + size))
                        else
                            echo -e "       ❌ ${RED}Lỗi khi xóa${NC}"
                        fi
                    else
                        echo -e "       🔍 ${PURPLE}[DRY RUN] Sẽ xóa${NC}"
                        TOTAL_SAVED=$((TOTAL_SAVED + size))
                    fi
                fi
            done < <(find "$search_dir" -name "$artifact" -type d -print0 2>/dev/null)
        done
    done
}

# Hàm dọn temp files
clean_temp_files() {
    echo -e "\n🗂️  ${BLUE}Dọn dẹp temp files...${NC}"
    
    local temp_patterns=(
        "*.log"
        "*.tmp"
        "*.temp"
        "*.cache"
        "*.bak"
        "*.swp"
        "*.swo"
        ".DS_Store"
        "Thumbs.db"
        "*.pid"
    )
    
    local search_dirs=(
        "$HOME/Documents"
        "$HOME/Desktop"
        "$HOME/Downloads"
    )
    
    for search_dir in "${search_dirs[@]}"; do
        if [ ! -d "$search_dir" ]; then
            continue
        fi
        
        echo -e "  📂 Quét: ${BLUE}$search_dir${NC}"
        local total_files=0
        local total_size=0
        
        for pattern in "${temp_patterns[@]}"; do
            while IFS= read -r -d '' file_path; do
                local file_size=$(stat -f%z "$file_path" 2>/dev/null || echo "0")
                total_files=$((total_files + 1))
                total_size=$((total_size + file_size))
                
                if [ "$DRY_RUN" = false ]; then
                    rm -f "$file_path" 2>/dev/null
                fi
            done < <(find "$search_dir" -name "$pattern" -type f -print0 2>/dev/null)
        done
        
        if [ $total_files -gt 0 ]; then
            local size_kb=$((total_size / 1024))
            echo -e "    📄 Tìm thấy $total_files files ($(format_size $size_kb))"
            
            if [ "$DRY_RUN" = false ]; then
                echo -e "       ✅ ${GREEN}Đã xóa${NC}"
            else
                echo -e "       🔍 ${PURPLE}[DRY RUN] Sẽ xóa${NC}"
            fi
            
            TOTAL_SAVED=$((TOTAL_SAVED + size_kb))
        fi
    done
}

# Hàm main
main() {
    echo -e "${BLUE}🍎 Quick macOS Development Environment Cleaner${NC}"
    echo "=================================================="
    
    if [ "$DRY_RUN" = true ]; then
        echo -e "${YELLOW}⚠️  CHẠY Ở CHẾ ĐỘ PHÂN TÍCH - Không có file nào bị xóa${NC}"
        echo -e "${YELLOW}   Sử dụng --execute để thực sự dọn dẹp${NC}"
    fi
    
    echo -e "\n🧹 ${BLUE}Dọn dẹp cache folders...${NC}"
    
    # Cache folders với process check
    clean_cache_folder "Xcode DerivedData" "$HOME/Library/Developer/Xcode/DerivedData" "Xcode"
    clean_cache_folder "iOS Simulator" "$HOME/Library/Developer/CoreSimulator/Caches" "Simulator"
    clean_cache_folder "npm cache" "$HOME/.npm/_cacache" "npm"
    clean_cache_folder "yarn cache" "$HOME/.yarn/cache" "yarn"
    clean_cache_folder "Gradle cache" "$HOME/.gradle/caches" "gradle"
    clean_cache_folder "Maven cache" "$HOME/.m2/repository" "mvn"
    clean_cache_folder "pip cache" "$HOME/.cache/pip" "pip"
    clean_cache_folder "Homebrew cache" "/usr/local/var/homebrew/cache" ""
    clean_cache_folder "macOS User Cache" "$HOME/Library/Caches" ""
    clean_cache_folder "macOS Logs" "$HOME/Library/Logs" ""
    
    # Build artifacts (nếu không chỉ cache)
    if [ "$CACHE_ONLY" != true ]; then
        clean_build_artifacts
        clean_temp_files
    fi
    
    # System cleanup
    if [ "$DRY_RUN" = false ]; then
        echo -e "\n🖥️  ${BLUE}System cleanup...${NC}"
        echo -e "  🔧 Purging memory..."
        sudo purge 2>/dev/null && echo -e "     ✅ ${GREEN}Hoàn thành${NC}" || echo -e "     ⚠️  ${YELLOW}Cần sudo${NC}"
        
        echo -e "  🔧 Clearing DNS cache..."
        sudo dscacheutil -flushcache 2>/dev/null && echo -e "     ✅ ${GREEN}Hoàn thành${NC}" || echo -e "     ⚠️  ${YELLOW}Cần sudo${NC}"
    fi
    
    # Báo cáo
    echo -e "\n${'='*50}"
    echo -e "${GREEN}📊 BÁO CÁO TỔNG KẾT${NC}"
    echo -e "${'='*50}"
    
    if [ "$DRY_RUN" = true ]; then
        echo -e "${PURPLE}🔍 [DRY RUN MODE] - Không có file nào bị xóa thực sự${NC}"
    fi
    
    echo -e "${GREEN}💾 Tổng dung lượng tiết kiệm: $(format_size $TOTAL_SAVED)${NC}"
    
    echo -e "\n${BLUE}💡 ĐỀ XUẤT:${NC}"
    if [ "$DRY_RUN" = true ]; then
        echo -e "   • Chạy lại với --execute để thực sự dọn dẹp"
    fi
    echo -e "   • Định kỳ chạy script này hàng tuần"
    echo -e "   • Backup dữ liệu quan trọng trước khi dọn dẹp"
    echo -e "   • Sử dụng --aggressive để dọn dẹp project cũ hơn"
}

# Xử lý tham số
CACHE_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --execute)
            DRY_RUN=false
            shift
            ;;
        --aggressive)
            AGGRESSIVE=true
            shift
            ;;
        --cache-only)
            CACHE_ONLY=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "Tham số không hợp lệ: $1"
            show_help
            exit 1
            ;;
    esac
done

# Kiểm tra bc command
if ! command -v bc &> /dev/null; then
    echo -e "${RED}❌ Lệnh 'bc' không tìm thấy. Cài đặt: brew install bc${NC}"
    exit 1
fi

# Chạy main
main
