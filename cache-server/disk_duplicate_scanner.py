#!/usr/bin/env python3
"""
Disk Duplicate Scanner for macOS
Quét toàn bộ ổ đĩa để tìm thư mục trùng lặp một cách hiệu quả và an toàn
"""

import os
import sys
import json
import hashlib
import argparse
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime
import subprocess
import threading
import time

class DiskDuplicateScanner:
    def __init__(self, scan_paths=None, exclude_paths=None, min_size_mb=1, max_depth=10):
        self.scan_paths = scan_paths or ['/Users', '/Applications', '/opt', '/usr/local']
        self.exclude_paths = exclude_paths or [
            '/System', '/Library/System', '/private/var', '/dev', '/proc',
            '/tmp', '/var/tmp', '/.Spotlight-V100', '/.fseventsd',
            '/.DocumentRevisions-V100', '/.Trashes'
        ]
        self.min_size_bytes = min_size_mb * 1024 * 1024
        self.max_depth = max_depth
        
        # Thống kê
        self.stats = {
            'folders_scanned': 0,
            'folders_skipped': 0,
            'duplicates_found': 0,
            'total_size_duplicates': 0,
            'scan_start_time': None,
            'scan_end_time': None
        }
        
        # Kết quả
        self.folder_groups = defaultdict(list)  # Nhóm theo tên
        self.size_groups = defaultdict(list)    # Nhóm theo kích thước
        self.content_groups = defaultdict(list) # Nhóm theo nội dung (hash)
        
        # Thread safety
        self.lock = threading.Lock()
        self.stop_scanning = False

    def is_excluded_path(self, path):
        """Kiểm tra xem đường dẫn có bị loại trừ không"""
        path_str = str(path)
        
        # Kiểm tra các đường dẫn bị loại trừ
        for exclude in self.exclude_paths:
            if path_str.startswith(exclude):
                return True
        
        # Kiểm tra các pattern đặc biệt
        if any(part.startswith('.') and part not in ['.git', '.vscode', '.idea'] 
               for part in path.parts):
            return True
            
        # Kiểm tra quyền truy cập
        try:
            os.access(path, os.R_OK)
        except (PermissionError, OSError):
            return True
            
        return False

    def get_folder_size(self, folder_path):
        """Tính kích thước thư mục"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(folder_path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except (OSError, FileNotFoundError):
                        pass
        except (OSError, PermissionError):
            pass
        return total_size

    def get_folder_signature(self, folder_path, quick=True):
        """Tạo signature cho thư mục để so sánh nội dung"""
        try:
            items = []
            
            # Lấy danh sách file và thư mục con
            for item in sorted(os.listdir(folder_path)):
                item_path = os.path.join(folder_path, item)
                try:
                    stat = os.stat(item_path)
                    if os.path.isfile(item_path):
                        if quick:
                            # Quick mode: chỉ dùng tên file và kích thước
                            items.append(f"f:{item}:{stat.st_size}")
                        else:
                            # Deep mode: thêm hash của file nhỏ
                            if stat.st_size < 1024 * 1024:  # < 1MB
                                with open(item_path, 'rb') as f:
                                    content_hash = hashlib.md5(f.read()).hexdigest()[:8]
                                items.append(f"f:{item}:{stat.st_size}:{content_hash}")
                            else:
                                items.append(f"f:{item}:{stat.st_size}")
                    elif os.path.isdir(item_path):
                        items.append(f"d:{item}")
                except (OSError, PermissionError):
                    continue
            
            # Tạo hash từ signature
            signature = '|'.join(items)
            return hashlib.md5(signature.encode()).hexdigest()
            
        except (OSError, PermissionError):
            return None

    def scan_folder(self, folder_path, current_depth=0):
        """Quét một thư mục"""
        if self.stop_scanning or current_depth > self.max_depth:
            return
            
        folder_path = Path(folder_path)
        
        if self.is_excluded_path(folder_path):
            with self.lock:
                self.stats['folders_skipped'] += 1
            return
        
        try:
            # Kiểm tra kích thước
            folder_size = self.get_folder_size(folder_path)
            if folder_size < self.min_size_bytes:
                with self.lock:
                    self.stats['folders_skipped'] += 1
                return
            
            # Thông tin thư mục
            folder_info = {
                'path': str(folder_path),
                'name': folder_path.name,
                'size': folder_size,
                'mtime': folder_path.stat().st_mtime,
                'depth': current_depth
            }
            
            # Nhóm theo tên
            with self.lock:
                self.folder_groups[folder_path.name].append(folder_info)
                self.stats['folders_scanned'] += 1
            
            # Nhóm theo kích thước (chỉ nếu có thư mục cùng tên)
            if len(self.folder_groups[folder_path.name]) > 1:
                size_key = f"{folder_path.name}_{folder_size}"
                with self.lock:
                    self.size_groups[size_key].append(folder_info)
            
            # Quét thư mục con
            try:
                for item in folder_path.iterdir():
                    if item.is_dir() and not self.stop_scanning:
                        self.scan_folder(item, current_depth + 1)
            except (PermissionError, OSError):
                pass
                
        except (OSError, PermissionError):
            with self.lock:
                self.stats['folders_skipped'] += 1

    def scan_disk(self, show_progress=True):
        """Quét toàn bộ ổ đĩa"""
        print("🔍 Bắt đầu quét ổ đĩa tìm thư mục trùng lặp...")
        print(f"📂 Đường dẫn quét: {', '.join(self.scan_paths)}")
        print(f"🚫 Loại trừ: {', '.join(self.exclude_paths[:3])}...")
        print(f"📏 Kích thước tối thiểu: {self.min_size_bytes // (1024*1024)}MB")
        print()
        
        self.stats['scan_start_time'] = datetime.now()
        
        # Progress tracking
        if show_progress:
            progress_thread = threading.Thread(target=self._show_progress)
            progress_thread.daemon = True
            progress_thread.start()
        
        try:
            # Quét từng đường dẫn
            for scan_path in self.scan_paths:
                if not os.path.exists(scan_path):
                    continue
                    
                print(f"📂 Đang quét: {scan_path}")
                self.scan_folder(scan_path)
                
                if self.stop_scanning:
                    break
        
        except KeyboardInterrupt:
            print("\n⏹️  Đang dừng quét...")
            self.stop_scanning = True
        
        self.stats['scan_end_time'] = datetime.now()
        
        if show_progress:
            time.sleep(1)  # Đợi progress thread kết thúc
        
        print(f"\n✅ Hoàn thành quét!")

    def _show_progress(self):
        """Hiển thị tiến trình quét"""
        while not self.stop_scanning:
            with self.lock:
                scanned = self.stats['folders_scanned']
                skipped = self.stats['folders_skipped']
                duplicates = len([name for name, folders in self.folder_groups.items() 
                                if len(folders) > 1])
            
            print(f"\r📊 Đã quét: {scanned:,} | Bỏ qua: {skipped:,} | Trùng lặp: {duplicates:,}", 
                  end='', flush=True)
            time.sleep(2)

    def analyze_duplicates(self, deep_analysis=False):
        """Phân tích thư mục trùng lặp"""
        print("\n🔬 Phân tích thư mục trùng lặp...")
        
        duplicates = {}
        
        for folder_name, folder_list in self.folder_groups.items():
            if len(folder_list) < 2:
                continue
            
            # Nhóm theo kích thước
            size_groups = defaultdict(list)
            for folder in folder_list:
                size_groups[folder['size']].append(folder)
            
            # Chỉ quan tâm nhóm có > 1 thư mục cùng kích thước
            for size, same_size_folders in size_groups.items():
                if len(same_size_folders) < 2:
                    continue
                
                group_key = f"{folder_name}_{size}"
                
                if deep_analysis:
                    # Phân tích sâu: so sánh nội dung
                    content_groups = defaultdict(list)
                    for folder in same_size_folders:
                        signature = self.get_folder_signature(folder['path'], quick=False)
                        if signature:
                            content_groups[signature].append(folder)
                    
                    # Chỉ lấy nhóm có nội dung giống nhau
                    for signature, content_folders in content_groups.items():
                        if len(content_folders) > 1:
                            duplicates[f"{group_key}_{signature[:8]}"] = {
                                'folders': content_folders,
                                'type': 'content_match',
                                'confidence': 'high'
                            }
                else:
                    # Phân tích nhanh: chỉ dựa vào tên và kích thước
                    duplicates[group_key] = {
                        'folders': same_size_folders,
                        'type': 'name_size_match',
                        'confidence': 'medium'
                    }
        
        return duplicates

    def format_size(self, size_bytes):
        """Format kích thước"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"

    def generate_report(self, duplicates, output_file=None):
        """Tạo báo cáo"""
        print("\n" + "="*80)
        print("📊 BÁO CÁO THƯMỤC TRÙNG LẶP")
        print("="*80)
        
        # Thống kê tổng quan
        scan_duration = (self.stats['scan_end_time'] - self.stats['scan_start_time']).total_seconds()
        total_duplicate_size = 0
        total_duplicate_folders = 0
        
        for group_data in duplicates.values():
            folders = group_data['folders']
            total_duplicate_folders += len(folders)
            # Tính size có thể tiết kiệm (giữ lại 1, xóa các folder còn lại)
            if len(folders) > 1:
                folder_size = folders[0]['size']
                total_duplicate_size += folder_size * (len(folders) - 1)
        
        print(f"⏱️  Thời gian quét: {scan_duration:.1f} giây")
        print(f"📁 Thư mục đã quét: {self.stats['folders_scanned']:,}")
        print(f"🚫 Thư mục bỏ qua: {self.stats['folders_skipped']:,}")
        print(f"🔍 Nhóm trùng lặp: {len(duplicates):,}")
        print(f"📂 Tổng thư mục trùng: {total_duplicate_folders:,}")
        print(f"💾 Dung lượng có thể tiết kiệm: {self.format_size(total_duplicate_size)}")
        
        # Chi tiết từng nhóm
        print(f"\n📋 CHI TIẾT CÁC NHÓM TRÙNG LẶP:")
        print("-" * 80)
        
        sorted_groups = sorted(duplicates.items(), 
                             key=lambda x: x[1]['folders'][0]['size'], 
                             reverse=True)
        
        for i, (group_key, group_data) in enumerate(sorted_groups[:20], 1):
            folders = group_data['folders']
            confidence = group_data['confidence']
            
            print(f"\n{i}. 📁 {folders[0]['name']}")
            print(f"   📏 Kích thước: {self.format_size(folders[0]['size'])}")
            print(f"   🎯 Độ tin cậy: {confidence}")
            print(f"   📊 Số lượng: {len(folders)} thư mục")
            
            for j, folder in enumerate(folders, 1):
                mtime = datetime.fromtimestamp(folder['mtime']).strftime('%Y-%m-%d %H:%M')
                print(f"      {j}. {folder['path']} (sửa đổi: {mtime})")
            
            if len(folders) > 1:
                potential_saving = folders[0]['size'] * (len(folders) - 1)
                print(f"   💰 Có thể tiết kiệm: {self.format_size(potential_saving)}")
        
        if len(duplicates) > 20:
            print(f"\n... và {len(duplicates) - 20} nhóm khác")
        
        # Xuất file JSON nếu được yêu cầu
        if output_file:
            self.export_json(duplicates, output_file)
            print(f"\n💾 Đã xuất chi tiết ra file: {output_file}")
        
        # Đề xuất
        print(f"\n💡 ĐỀ XUẤT:")
        print("   • Kiểm tra kỹ từng nhóm trước khi xóa")
        print("   • Ưu tiên xóa thư mục có độ tin cậy 'high'")
        print("   • Backup dữ liệu quan trọng trước khi xóa")
        print("   • Sử dụng --deep-analysis để phân tích chính xác hơn")

    def export_json(self, duplicates, output_file):
        """Xuất kết quả ra file JSON"""
        export_data = {
            'scan_info': {
                'scan_paths': self.scan_paths,
                'exclude_paths': self.exclude_paths,
                'min_size_mb': self.min_size_bytes // (1024*1024),
                'scan_time': self.stats['scan_start_time'].isoformat(),
                'duration_seconds': (self.stats['scan_end_time'] - self.stats['scan_start_time']).total_seconds()
            },
            'statistics': self.stats,
            'duplicates': duplicates
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)

def main():
    parser = argparse.ArgumentParser(description='Disk Duplicate Scanner for macOS')
    parser.add_argument('--paths', nargs='+', 
                       default=['/Users', '/Applications', '/opt', '/usr/local'],
                       help='Đường dẫn để quét (mặc định: /Users /Applications /opt /usr/local)')
    parser.add_argument('--exclude', nargs='+',
                       default=['/System', '/Library/System', '/private/var'],
                       help='Đường dẫn loại trừ')
    parser.add_argument('--min-size', type=int, default=1,
                       help='Kích thước tối thiểu (MB) để xem xét (mặc định: 1)')
    parser.add_argument('--max-depth', type=int, default=10,
                       help='Độ sâu tối đa để quét (mặc định: 10)')
    parser.add_argument('--deep-analysis', action='store_true',
                       help='Phân tích sâu bằng cách so sánh nội dung thư mục')
    parser.add_argument('--output', type=str,
                       help='File để xuất kết quả JSON')
    parser.add_argument('--no-progress', action='store_true',
                       help='Không hiển thị tiến trình')
    
    args = parser.parse_args()
    
    print("💿 Disk Duplicate Scanner for macOS")
    print("="*50)
    
    # Tạo scanner
    scanner = DiskDuplicateScanner(
        scan_paths=args.paths,
        exclude_paths=args.exclude,
        min_size_mb=args.min_size,
        max_depth=args.max_depth
    )
    
    try:
        # Quét ổ đĩa
        scanner.scan_disk(show_progress=not args.no_progress)
        
        # Phân tích
        duplicates = scanner.analyze_duplicates(deep_analysis=args.deep_analysis)
        
        # Báo cáo
        scanner.generate_report(duplicates, output_file=args.output)
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Đã hủy bởi người dùng")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Lỗi: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
