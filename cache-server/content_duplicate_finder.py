#!/usr/bin/env python3
"""
Content-based Duplicate Finder
T<PERSON><PERSON> thư mục trùng lặp dựa trên nội dung thực tế, không chỉ tên và kích thước
"""

import os
import sys
import hashlib
import argparse
from pathlib import Path
from collections import defaultdict
import json
from datetime import datetime

class ContentDuplicateFinder:
    def __init__(self, hash_algorithm='md5', quick_mode=True):
        self.hash_algorithm = hash_algorithm
        self.quick_mode = quick_mode
        self.folder_hashes = {}
        self.duplicate_groups = defaultdict(list)
        
    def calculate_file_hash(self, file_path, chunk_size=8192):
        """Tính hash của một file"""
        hash_obj = hashlib.new(self.hash_algorithm)
        try:
            with open(file_path, 'rb') as f:
                if self.quick_mode:
                    # Quick mode: chỉ hash 64KB đầu và cuối
                    chunk = f.read(chunk_size * 8)  # 64KB
                    if chunk:
                        hash_obj.update(chunk)
                    
                    # <PERSON><PERSON><PERSON><PERSON> đến cuối file
                    f.seek(-min(chunk_size * 8, f.tell()), 2)
                    chunk = f.read()
                    if chunk:
                        hash_obj.update(chunk)
                else:
                    # Full mode: hash toàn bộ file
                    while chunk := f.read(chunk_size):
                        hash_obj.update(chunk)
        except (IOError, OSError):
            return None
        
        return hash_obj.hexdigest()
    
    def calculate_folder_signature(self, folder_path):
        """Tính signature của thư mục dựa trên nội dung"""
        folder_path = Path(folder_path)
        
        if not folder_path.exists() or not folder_path.is_dir():
            return None
        
        try:
            # Thu thập thông tin về tất cả files và folders
            items = []
            
            for item in sorted(folder_path.rglob('*')):
                relative_path = item.relative_to(folder_path)
                
                if item.is_file():
                    # File: tên + kích thước + hash (nếu nhỏ)
                    try:
                        size = item.stat().st_size
                        if size < 1024 * 1024:  # < 1MB
                            file_hash = self.calculate_file_hash(item)
                            if file_hash:
                                items.append(f"f:{relative_path}:{size}:{file_hash}")
                            else:
                                items.append(f"f:{relative_path}:{size}")
                        else:
                            # File lớn: chỉ dùng tên và kích thước
                            items.append(f"f:{relative_path}:{size}")
                    except (OSError, IOError):
                        items.append(f"f:{relative_path}:0")
                        
                elif item.is_dir():
                    # Directory: chỉ tên
                    items.append(f"d:{relative_path}")
            
            # Tạo hash từ signature
            signature = '\n'.join(items)
            hash_obj = hashlib.new(self.hash_algorithm)
            hash_obj.update(signature.encode('utf-8'))
            
            return hash_obj.hexdigest()
            
        except (OSError, PermissionError):
            return None
    
    def scan_folders(self, search_paths, min_size_mb=1, max_depth=5):
        """Quét và tính hash cho các thư mục"""
        print("🔍 Đang quét và tính hash thư mục...")
        
        min_size_bytes = min_size_mb * 1024 * 1024
        scanned_count = 0
        
        for search_path in search_paths:
            search_path = Path(search_path)
            if not search_path.exists():
                continue
                
            print(f"📂 Quét: {search_path}")
            
            try:
                for folder_path in search_path.rglob('*'):
                    if not folder_path.is_dir():
                        continue
                    
                    # Kiểm tra độ sâu
                    try:
                        depth = len(folder_path.relative_to(search_path).parts)
                        if depth > max_depth:
                            continue
                    except ValueError:
                        continue
                    
                    # Kiểm tra kích thước
                    try:
                        total_size = sum(f.stat().st_size for f in folder_path.rglob('*') if f.is_file())
                        if total_size < min_size_bytes:
                            continue
                    except (OSError, PermissionError):
                        continue
                    
                    # Tính hash
                    folder_hash = self.calculate_folder_signature(folder_path)
                    if folder_hash:
                        self.folder_hashes[str(folder_path)] = {
                            'hash': folder_hash,
                            'size': total_size,
                            'name': folder_path.name,
                            'mtime': folder_path.stat().st_mtime
                        }
                        scanned_count += 1
                        
                        if scanned_count % 100 == 0:
                            print(f"   📊 Đã quét: {scanned_count} thư mục")
                            
            except (OSError, PermissionError) as e:
                print(f"   ⚠️  Lỗi quét {search_path}: {e}")
        
        print(f"✅ Hoàn thành quét {scanned_count} thư mục")
        return scanned_count
    
    def find_duplicates(self):
        """Tìm thư mục trùng lặp dựa trên hash"""
        print("\n🔬 Phân tích thư mục trùng lặp...")
        
        # Nhóm theo hash
        hash_groups = defaultdict(list)
        for folder_path, folder_info in self.folder_hashes.items():
            hash_groups[folder_info['hash']].append((folder_path, folder_info))
        
        # Lọc ra nhóm có > 1 thư mục
        for folder_hash, folders in hash_groups.items():
            if len(folders) > 1:
                self.duplicate_groups[folder_hash] = folders
        
        print(f"🔍 Tìm thấy {len(self.duplicate_groups)} nhóm thư mục trùng lặp")
        return len(self.duplicate_groups)
    
    def format_size(self, size_bytes):
        """Format kích thước"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"
    
    def generate_report(self, output_file=None):
        """Tạo báo cáo chi tiết"""
        print("\n" + "="*80)
        print("📊 BÁO CÁO THƯMỤC TRÙNG LẶP (DỰA TRÊN NỘI DUNG)")
        print("="*80)
        
        total_duplicate_folders = 0
        total_potential_savings = 0
        
        # Sắp xếp theo kích thước giảm dần
        sorted_groups = sorted(
            self.duplicate_groups.items(),
            key=lambda x: x[1][0][1]['size'],
            reverse=True
        )
        
        for i, (folder_hash, folders) in enumerate(sorted_groups, 1):
            folder_size = folders[0][1]['size']
            folder_name = folders[0][1]['name']
            
            print(f"\n{i}. 📁 {folder_name}")
            print(f"   🔑 Hash: {folder_hash[:16]}...")
            print(f"   📏 Kích thước: {self.format_size(folder_size)}")
            print(f"   📊 Số lượng: {len(folders)} thư mục")
            print(f"   📍 Đường dẫn:")
            
            for j, (folder_path, folder_info) in enumerate(folders, 1):
                mtime = datetime.fromtimestamp(folder_info['mtime']).strftime('%Y-%m-%d %H:%M')
                print(f"      {j}. {folder_path}")
                print(f"         📅 Sửa đổi: {mtime}")
            
            # Tính tiết kiệm
            potential_saving = folder_size * (len(folders) - 1)
            total_potential_savings += potential_saving
            total_duplicate_folders += len(folders)
            
            print(f"   💰 Có thể tiết kiệm: {self.format_size(potential_saving)}")
            print("   " + "-"*70)
        
        # Thống kê tổng
        print(f"\n📈 THỐNG KÊ TỔNG:")
        print(f"   🔍 Nhóm trùng lặp: {len(self.duplicate_groups)}")
        print(f"   📁 Tổng thư mục trùng: {total_duplicate_folders}")
        print(f"   💾 Tổng có thể tiết kiệm: {self.format_size(total_potential_savings)}")
        
        # Xuất JSON nếu được yêu cầu
        if output_file:
            self.export_json(output_file)
            print(f"\n💾 Đã xuất chi tiết ra: {output_file}")
        
        print(f"\n💡 LƯU Ý:")
        print("   • Các thư mục này có nội dung HOÀN TOÀN GIỐNG NHAU")
        print("   • Độ tin cậy cao - an toàn để xóa")
        print("   • Nên giữ lại thư mục có đường dẫn ngắn nhất hoặc mới nhất")
        print("   • Backup trước khi xóa để đảm bảo an toàn")
    
    def export_json(self, output_file):
        """Xuất kết quả ra file JSON"""
        export_data = {
            'scan_info': {
                'hash_algorithm': self.hash_algorithm,
                'quick_mode': self.quick_mode,
                'scan_time': datetime.now().isoformat(),
                'total_folders_scanned': len(self.folder_hashes)
            },
            'duplicates': {}
        }
        
        for folder_hash, folders in self.duplicate_groups.items():
            export_data['duplicates'][folder_hash] = {
                'folders': [
                    {
                        'path': folder_path,
                        'size': folder_info['size'],
                        'name': folder_info['name'],
                        'mtime': folder_info['mtime']
                    }
                    for folder_path, folder_info in folders
                ]
            }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
    
    def suggest_cleanup_actions(self):
        """Đề xuất hành động dọn dẹp"""
        print(f"\n🧹 ĐỀ XUẤT DỌNP DẸP:")
        
        for folder_hash, folders in self.duplicate_groups.items():
            if len(folders) < 2:
                continue
            
            # Sắp xếp theo tiêu chí: mtime mới nhất, đường dẫn ngắn nhất
            sorted_folders = sorted(
                folders,
                key=lambda x: (-x[1]['mtime'], len(x[0]))
            )
            
            keep_folder = sorted_folders[0]
            remove_folders = sorted_folders[1:]
            
            print(f"\n📁 Nhóm: {keep_folder[1]['name']}")
            print(f"   🔒 Giữ lại: {keep_folder[0]}")
            print(f"   🗑️  Xóa:")
            
            for folder_path, folder_info in remove_folders:
                print(f"      • {folder_path}")

def main():
    parser = argparse.ArgumentParser(description='Content-based Duplicate Finder')
    parser.add_argument('paths', nargs='+',
                       help='Đường dẫn để quét')
    parser.add_argument('--algorithm', choices=['md5', 'sha1', 'sha256'],
                       default='md5', help='Thuật toán hash (mặc định: md5)')
    parser.add_argument('--full-hash', action='store_true',
                       help='Hash toàn bộ file (chậm nhưng chính xác hơn)')
    parser.add_argument('--min-size', type=int, default=1,
                       help='Kích thước tối thiểu (MB) (mặc định: 1)')
    parser.add_argument('--max-depth', type=int, default=5,
                       help='Độ sâu tối đa (mặc định: 5)')
    parser.add_argument('--output', type=str,
                       help='File để xuất kết quả JSON')
    parser.add_argument('--suggest-cleanup', action='store_true',
                       help='Đề xuất hành động dọn dẹp')
    
    args = parser.parse_args()
    
    print("🔍 Content-based Duplicate Finder")
    print("="*50)
    print(f"🔑 Thuật toán hash: {args.algorithm}")
    print(f"⚡ Chế độ: {'Full hash' if args.full_hash else 'Quick hash'}")
    print(f"📏 Kích thước tối thiểu: {args.min_size}MB")
    print(f"📊 Độ sâu tối đa: {args.max_depth}")
    print()
    
    # Tạo finder
    finder = ContentDuplicateFinder(
        hash_algorithm=args.algorithm,
        quick_mode=not args.full_hash
    )
    
    try:
        # Quét thư mục
        scanned_count = finder.scan_folders(
            args.paths,
            min_size_mb=args.min_size,
            max_depth=args.max_depth
        )
        
        if scanned_count == 0:
            print("⚠️  Không tìm thấy thư mục nào phù hợp điều kiện")
            return
        
        # Tìm duplicates
        duplicate_count = finder.find_duplicates()
        
        if duplicate_count == 0:
            print("✅ Không tìm thấy thư mục trùng lặp nào!")
            return
        
        # Báo cáo
        finder.generate_report(output_file=args.output)
        
        # Đề xuất dọn dẹp
        if args.suggest_cleanup:
            finder.suggest_cleanup_actions()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Đã hủy bởi người dùng")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Lỗi: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
