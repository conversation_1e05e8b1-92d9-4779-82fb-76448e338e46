#!/bin/bash

# Script để kiểm tra và dọn dẹp thư mục trùng lặp
# Author: Auto-generated
# Usage: ./check-and-clean-duplicates.sh [--clean] [--path /path/to/search]

# Màu sắc cho output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Biến mặc định
SEARCH_PATH="/Users/<USER>/Documents/"
CLEAN_MODE=false
INTERACTIVE_MODE=true

# Hàm hiển thị help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "OPTIONS:"
    echo "  --clean              Tự động xóa thư mục trùng (giữ lại thư mục có kích thước lớn nhất)"
    echo "  --path PATH          Đường dẫn để tìm kiếm (mặc định: /Users/<USER>/Documents/)"
    echo "  --non-interactive    Không hỏi xác nhận khi xóa"
    echo "  --help               Hiển thị help này"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Chỉ kiểm tra và hiển thị"
    echo "  $0 --clean                          # Kiểm tra và dọn dẹp tự động"
    echo "  $0 --path /Users/<USER>/Desktop/     # Tìm kiếm trong Desktop"
    echo "  $0 --clean --non-interactive        # Dọn dẹp không hỏi xác nhận"
}

# Xử lý tham số dòng lệnh
while [[ $# -gt 0 ]]; do
    case $1 in
        --clean)
            CLEAN_MODE=true
            shift
            ;;
        --path)
            SEARCH_PATH="$2"
            shift 2
            ;;
        --non-interactive)
            INTERACTIVE_MODE=false
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "Tham số không hợp lệ: $1"
            show_help
            exit 1
            ;;
    esac
done

# Kiểm tra đường dẫn có tồn tại không
if [ ! -d "$SEARCH_PATH" ]; then
    echo -e "${RED}❌ Đường dẫn không tồn tại: $SEARCH_PATH${NC}"
    exit 1
fi

echo -e "${BLUE}🔍 Đang tìm kiếm thư mục trùng lặp trong: $SEARCH_PATH${NC}"
echo ""

# Tạo file tạm để lưu kết quả
TEMP_FILE=$(mktemp)
DUPLICATES_FOUND=false

# Tìm tất cả thư mục và nhóm theo tên
find "$SEARCH_PATH" -type d -exec basename {} \; | sort | uniq -d > "$TEMP_FILE"

# Kiểm tra xem có thư mục trùng lặp không
if [ ! -s "$TEMP_FILE" ]; then
    echo -e "${GREEN}✅ Không tìm thấy thư mục trùng lặp nào!${NC}"
    rm "$TEMP_FILE"
    exit 0
fi

echo -e "${YELLOW}📋 Danh sách thư mục trùng lặp:${NC}"
echo "=================================="

# Xử lý từng thư mục trùng lặp
while read -r folder_name; do
    if [ -z "$folder_name" ]; then
        continue
    fi
    
    DUPLICATES_FOUND=true
    echo ""
    echo -e "${RED}📁 Thư mục trùng: ${YELLOW}$folder_name${NC}"
    
    # Tìm tất cả đường dẫn của thư mục trùng này
    PATHS_FILE=$(mktemp)
    find "$SEARCH_PATH" -type d -name "$folder_name" > "$PATHS_FILE"
    
    # Hiển thị thông tin chi tiết về từng thư mục
    declare -a paths_array
    declare -a sizes_array
    largest_size=0
    largest_path=""
    
    while read -r path; do
        if [ -d "$path" ]; then
            # Tính kích thước thư mục
            size=$(du -s "$path" 2>/dev/null | cut -f1)
            size_human=$(du -sh "$path" 2>/dev/null | cut -f1)
            
            paths_array+=("$path")
            sizes_array+=("$size")
            
            echo -e "   📍 $path ${BLUE}($size_human)${NC}"
            
            # Tìm thư mục có kích thước lớn nhất
            if [ "$size" -gt "$largest_size" ]; then
                largest_size=$size
                largest_path="$path"
            fi
        fi
    done < "$PATHS_FILE"
    
    # Nếu ở chế độ clean
    if [ "$CLEAN_MODE" = true ]; then
        echo -e "   ${GREEN}🔒 Giữ lại: $largest_path (lớn nhất)${NC}"
        
        # Xóa các thư mục khác
        for i in "${!paths_array[@]}"; do
            current_path="${paths_array[$i]}"
            if [ "$current_path" != "$largest_path" ]; then
                if [ "$INTERACTIVE_MODE" = true ]; then
                    echo -n -e "   ${YELLOW}❓ Xóa $current_path? (y/N): ${NC}"
                    read -r confirm
                    if [[ $confirm =~ ^[Yy]$ ]]; then
                        rm -rf "$current_path"
                        echo -e "   ${RED}🗑️  Đã xóa: $current_path${NC}"
                    else
                        echo -e "   ${BLUE}⏭️  Bỏ qua: $current_path${NC}"
                    fi
                else
                    rm -rf "$current_path"
                    echo -e "   ${RED}🗑️  Đã xóa: $current_path${NC}"
                fi
            fi
        done
    fi
    
    rm "$PATHS_FILE"
    echo "-----------------------------"
done < "$TEMP_FILE"

rm "$TEMP_FILE"

if [ "$DUPLICATES_FOUND" = true ]; then
    echo ""
    if [ "$CLEAN_MODE" = false ]; then
        echo -e "${YELLOW}💡 Để tự động dọn dẹp, chạy lại với tham số --clean${NC}"
        echo -e "${YELLOW}💡 Ví dụ: $0 --clean${NC}"
    else
        echo -e "${GREEN}✅ Hoàn thành dọn dẹp thư mục trùng lặp!${NC}"
    fi
else
    echo -e "${GREEN}✅ Không có thư mục trùng lặp nào!${NC}"
fi
