#!/bin/bash

# Test script để demo các tính năng quét ổ đĩa
# Tạo thư mục test và demo các script

# Màu sắc
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🧪 TEST DISK DUPLICATE SCANNER${NC}"
echo "=================================="
echo ""

# Tạo thư mục test
TEST_DIR="test_duplicates"
echo -e "${YELLOW}📁 Tạo thư mục test...${NC}"

if [ -d "$TEST_DIR" ]; then
    rm -rf "$TEST_DIR"
fi

mkdir -p "$TEST_DIR"/{folder1,folder2,folder3,unique_folder}

# Tạo nội dung giống nhau cho folder1 và folder2
echo "Test content 1" > "$TEST_DIR/folder1/file1.txt"
echo "Test content 2" > "$TEST_DIR/folder1/file2.txt"
mkdir -p "$TEST_DIR/folder1/subfolder"
echo "Sub content" > "$TEST_DIR/folder1/subfolder/subfile.txt"

# Copy để tạo duplicate
cp -r "$TEST_DIR/folder1/"* "$TEST_DIR/folder2/"

# Tạo folder3 với tên giống nhưng nội dung khác
echo "Different content" > "$TEST_DIR/folder3/file1.txt"
echo "Test content 2" > "$TEST_DIR/folder3/file2.txt"

# Tạo unique folder
echo "Unique content" > "$TEST_DIR/unique_folder/unique.txt"

echo -e "${GREEN}✅ Đã tạo thư mục test với:${NC}"
echo "   • folder1 và folder2: nội dung giống nhau (duplicate)"
echo "   • folder3: tên file giống nhưng nội dung khác"
echo "   • unique_folder: hoàn toàn khác biệt"
echo ""

# Test 1: Quick disk scan
echo -e "${BLUE}🔍 TEST 1: Quick Disk Scan${NC}"
echo "Lệnh: ./quick_disk_scan.sh --paths $TEST_DIR --details --min-size 0"
echo ""
./quick_disk_scan.sh --paths "$TEST_DIR" --details --min-size 0
echo ""

# Test 2: Python disk scanner
echo -e "${BLUE}🔍 TEST 2: Python Disk Scanner${NC}"
echo "Lệnh: python3 disk_duplicate_scanner.py --paths $TEST_DIR --min-size 0"
echo ""
python3 disk_duplicate_scanner.py --paths "$TEST_DIR" --min-size 0
echo ""

# Test 3: Content duplicate finder
echo -e "${BLUE}🔍 TEST 3: Content Duplicate Finder${NC}"
echo "Lệnh: python3 content_duplicate_finder.py $TEST_DIR --min-size 0"
echo ""
python3 content_duplicate_finder.py "$TEST_DIR" --min-size 0
echo ""

# Test 4: Deep analysis
echo -e "${BLUE}🔍 TEST 4: Deep Analysis${NC}"
echo "Lệnh: python3 disk_duplicate_scanner.py --paths $TEST_DIR --deep-analysis --min-size 0"
echo ""
python3 disk_duplicate_scanner.py --paths "$TEST_DIR" --deep-analysis --min-size 0
echo ""

# Cleanup
echo -e "${YELLOW}🧹 Dọn dẹp thư mục test...${NC}"
read -p "Xóa thư mục test? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -rf "$TEST_DIR"
    echo -e "${GREEN}✅ Đã xóa thư mục test${NC}"
else
    echo -e "${BLUE}📁 Thư mục test được giữ lại: $TEST_DIR${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Hoàn thành test!${NC}"
echo -e "${BLUE}💡 Các script hoạt động tốt và có thể phát hiện:${NC}"
echo "   • Thư mục trùng lặp theo tên và kích thước"
echo "   • Thư mục trùng lặp theo nội dung thực tế"
echo "   • Phân biệt được thư mục có tên giống nhưng nội dung khác"
