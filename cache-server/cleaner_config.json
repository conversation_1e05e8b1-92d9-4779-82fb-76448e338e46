{"cache_paths": {"macos_system": {"user_cache": "~/Library/Caches", "user_logs": "~/Library/Logs", "temp": "/tmp", "trash": "~/.T<PERSON>"}, "development_tools": {"xcode_derived_data": "~/Library/Developer/Xcode/DerivedData", "xcode_archives": "~/Library/Developer/Xcode/Archives", "xcode_device_support": "~/Library/Developer/Xcode/iOS DeviceSupport", "simulator_cache": "~/Library/Developer/CoreSimulator/Caches", "simulator_devices": "~/Library/Developer/CoreSimulator/Devices"}, "java_ecosystem": {"jetbrains_cache": "~/.cache/JetBrains", "maven_repository": "~/.m2/repository", "gradle_cache": "~/.gradle/caches", "gradle_daemon": "~/.gradle/daemon", "gradle_wrapper": "~/.gradle/wrapper/dists", "intellij_system": "~/Library/Caches/IntelliJIdea*", "android_studio_system": "~/Library/Caches/AndroidStudio*"}, "nodejs_ecosystem": {"npm_cache": "~/.npm/_cacache", "npm_logs": "~/.npm/_logs", "yarn_cache": "~/.yarn/cache", "yarn_global": "~/.config/yarn/global/node_modules", "pnpm_store": "~/.pnpm-store", "node_gyp": "~/.node-gyp"}, "android_development": {"android_cache": "~/.android/cache", "android_avd": "~/.android/avd/*.avd/cache", "android_build_cache": "~/.android/build-cache", "gradle_android_cache": "~/.gradle/caches/modules-2/files-2.1"}, "ios_development": {"cocoapods_cache": "~/.cocoapods/repos", "carthage_cache": "~/Library/Caches/org.carthage.CarthageKit", "swift_pm_cache": "~/Library/Caches/org.swift.swiftpm"}, "other_tools": {"docker_cache": "~/.docker", "pip_cache": "~/.cache/pip", "homebrew_cache": "/usr/local/var/homebrew/cache", "homebrew_logs": "/usr/local/var/log", "git_credential_cache": "~/.git-credential-cache"}}, "build_artifacts": {"common": ["node_modules", "build", "dist", "out", "target", ".gradle", "DerivedData", ".next", ".nuxt", "coverage", ".nyc_output", "__pycache__", ".pytest_cache", ".tox", "vendor/bundle", ".bundle"], "web_development": [".cache", ".parcel-cache", ".webpack", ".rollup.cache", ".vite", ".turbo"], "mobile_development": ["Pods", "Carthage/Build", ".build", "DerivedData"], "java_artifacts": ["target", "build", ".gradle", "out", ".idea/caches"]}, "temp_file_extensions": [".log", ".tmp", ".temp", ".cache", ".bak", ".backup", ".swp", ".swo", ".DS_Store", ".localized", "Thumbs.db", ".pid", ".lock", "*.orig", "*.rej"], "safety_rules": {"active_project_indicators": [".git/logs/HEAD", ".git/FETCH_HEAD", "package-lock.json", "yarn.lock", "Podfile.lock", "Gemfile.lock"], "active_project_max_days": 30, "recent_file_max_days": 7, "process_checks": {"Xcode": ["Xcode", "xcodebuild", "xcrun"], "Android Studio": ["studio", "gradle"], "IntelliJ": ["idea", "intellij"], "npm": ["npm", "node"], "yarn": ["yarn"], "gradle": ["gradle", "gradlew"]}, "protected_paths": ["~/Library/Application Support", "~/Library/Preferences", "~/Library/Keychains", "~/Documents", "~/Desktop", "/System", "/usr/bin", "/usr/sbin"]}, "size_thresholds": {"min_cache_size_mb": 1, "min_build_artifact_size_mb": 1, "min_temp_file_size_kb": 1, "large_file_threshold_gb": 1}, "cleanup_strategies": {"conservative": {"description": "Chỉ dọn cache và temp files an toàn", "clean_cache": true, "clean_build_artifacts": false, "clean_temp_files": true, "check_active_projects": true, "max_project_age_days": 7}, "moderate": {"description": "Dọn cache và build artifacts của project cũ", "clean_cache": true, "clean_build_artifacts": true, "clean_temp_files": true, "check_active_projects": true, "max_project_age_days": 30}, "aggressive": {"description": "<PERSON><PERSON><PERSON> tất cả trừ project rất gần đây", "clean_cache": true, "clean_build_artifacts": true, "clean_temp_files": true, "check_active_projects": true, "max_project_age_days": 3}}, "system_commands": {"macos": {"purge_memory": ["sudo", "purge"], "flush_dns": ["sudo", "dscacheutil", "-flushcache"], "rebuild_launch_services": ["sudo", "/System/Library/Frameworks/CoreServices.framework/Frameworks/LaunchServices.framework/Support/lsregister", "-kill", "-r", "-domain", "local", "-domain", "system", "-domain", "user"], "clear_font_cache": ["sudo", "<PERSON><PERSON><PERSON>", "databases", "-remove"], "rebuild_spotlight": ["sudo", "mdutil", "-E", "/"]}}, "exclusions": {"never_delete": ["~/Library/Application Support/Code", "~/Library/Application Support/Google/Chrome", "~/Library/Application Support/Firefox", "~/.ssh", "~/.gnupg", "~/Library/Keychains"], "skip_if_process_running": {"Xcode": ["~/Library/Developer/Xcode/DerivedData"], "Android Studio": ["~/.gradle", "~/.android"], "npm": ["~/.npm"], "Docker": ["~/.docker"]}}, "reporting": {"show_details": true, "group_by_category": true, "show_size_breakdown": true, "export_json": false, "log_file": "~/Desktop/cleanup_log.txt"}}