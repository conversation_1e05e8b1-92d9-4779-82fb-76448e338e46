#!/bin/bash

# Demo script để test các tính năng của macOS Development Environment Cleaner

echo "🍎 macOS Development Environment Cleaner - DEMO"
echo "================================================"
echo ""

# Màu sắc
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}📋 Danh sách các script có sẵn:${NC}"
echo ""
echo "🍎 DỌNP DẸP MÔI TRƯỜNG PHÁT TRIỂN:"
echo "1. macos_dev_cleaner.py - Script Python toàn diện"
echo "2. quick_dev_clean.sh - Script bash nhanh"
echo ""
echo "💿 QUÉT Ổ ĐĨA TÌM THƯ MỤC TRÙNG LẶP:"
echo "3. disk_duplicate_scanner.py - Quét toàn bộ ổ đĩa"
echo "4. quick_disk_scan.sh - Q<PERSON>t nhanh ổ đĩa"
echo "5. content_duplicate_finder.py - Tìm trùng theo nội dung"
echo ""
echo "📁 DỌNP THƯ MỤC TRÙNG LẶP (CỤC BỘ):"
echo "6. check-and-clean-duplicates.sh - Dọn thư mục trùng lặp"
echo "7. duplicate_cleaner.py - Python dọn thư mục trùng lặp"
echo ""

echo -e "${YELLOW}🔍 DEMO 1: Phân tích hệ thống với Python script${NC}"
echo "Lệnh: python3 macos_dev_cleaner.py"
echo "Mô tả: Phân tích toàn diện cache và build artifacts (không xóa gì)"
echo ""

echo -e "${YELLOW}🧹 DEMO 2: Dọn dẹp nhanh với Bash script${NC}"
echo "Lệnh: ./quick_dev_clean.sh"
echo "Mô tả: Phân tích nhanh các cache phổ biến"
echo ""

echo -e "${YELLOW}💿 DEMO 3: Quét ổ đĩa tìm thư mục trùng lặp${NC}"
echo "Lệnh: python3 disk_duplicate_scanner.py --paths ~/Documents ~/Downloads"
echo "Mô tả: Quét toàn diện tìm thư mục trùng lặp trên ổ đĩa"
echo ""

echo -e "${YELLOW}⚡ DEMO 4: Quét nhanh ổ đĩa${NC}"
echo "Lệnh: ./quick_disk_scan.sh --details --min-size 5"
echo "Mô tả: Quét nhanh tìm thư mục trùng >= 5MB"
echo ""

echo -e "${YELLOW}🔬 DEMO 5: Tìm thư mục trùng theo nội dung${NC}"
echo "Lệnh: python3 content_duplicate_finder.py ~/Documents"
echo "Mô tả: So sánh nội dung thực tế để tìm thư mục hoàn toàn giống nhau"
echo ""

echo -e "${YELLOW}📁 DEMO 6: Tìm thư mục trùng lặp cục bộ${NC}"
echo "Lệnh: ./check-and-clean-duplicates.sh --path ~/Documents"
echo "Mô tả: Tìm và hiển thị thư mục trùng lặp trong Documents"
echo ""

echo -e "${GREEN}💡 Để chạy demo thực tế:${NC}"
echo ""
echo "# Phân tích hệ thống (an toàn)"
echo "python3 macos_dev_cleaner.py"
echo ""
echo "# Phân tích nhanh"
echo "./quick_dev_clean.sh"
echo ""
echo "# Quét ổ đĩa tìm thư mục trùng"
echo "python3 disk_duplicate_scanner.py"
echo "./quick_disk_scan.sh"
echo ""
echo "# Tìm thư mục trùng theo nội dung"
echo "python3 content_duplicate_finder.py ~/Documents"
echo ""
echo "# Tìm thư mục trùng lặp cục bộ"
echo "./check-and-clean-duplicates.sh"
echo ""
echo "# Xem help"
echo "python3 macos_dev_cleaner.py --help"
echo "./quick_dev_clean.sh --help"
echo "python3 disk_duplicate_scanner.py --help"
echo ""

echo -e "${YELLOW}⚠️  Lưu ý: Tất cả lệnh trên chỉ PHÂN TÍCH, không xóa gì.${NC}"
echo -e "${YELLOW}   Để thực sự dọn dẹp, thêm tham số --execute${NC}"
echo ""

echo -e "${BLUE}📖 Đọc README.md để biết thêm chi tiết!${NC}"
