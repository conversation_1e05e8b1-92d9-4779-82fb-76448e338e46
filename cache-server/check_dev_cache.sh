#!/bin/bash

echo "🧠 ĐÁNH GIÁ CACHE PHÁT TRIỂN & HỆ THỐNG TRÊN MACOS"
echo "---------------------------------------------------"

function check_cache() {
  local path=$1
  local name=$2
  if [ -d "$path" ]; then
    size=$(du -sh "$path" 2>/dev/null | cut -f1)
    echo "📁 $name: $size ($path)"
  else
    echo "📁 $name: Không tìm thấy"
  fi
}

echo ""
echo "🔸 JAVA:"
check_cache ~/.gradle "Gradle Cache"
check_cache ~/.m2 "Maven Cache"

echo ""
echo "🔸 NODE.JS:"
check_cache ~/.npm "npm Cache"
check_cache ~/.cache/yarn "Yarn Cache"
check_cache ~/.node-gyp "node-gyp Cache"

echo ""
echo "🔸 PYTHON:"
check_cache ~/Library/Caches/pip "pip Cache"
check_cache ~/.cache/pipenv "pipenv Cache"
check_cache ~/.pyenv "pyenv Environments"

echo ""
echo "🔸 VS CODE & EDITORS:"
check_cache ~/Library/Application\ Support/Code/Cache "VSCode Cache"
check_cache ~/Library/Application\ Support/JetBrains "JetBrains IDEs"

echo ""
echo "🔸 HỆ THỐNG KHÁC:"
check_cache ~/Library/Caches "User Library Cache"
check_cache /Library/Caches "System Library Cache"
check_cache ~/Library/Developer/Xcode/DerivedData "Xcode DerivedData"
check_cache ~/Library/Containers/com.docker.docker "Docker Desktop"
check_cache ~/Library/Caches/Homebrew "Homebrew Cache"
check_cache ~/Library/Logs "Log files"

echo ""
echo "🔍 GỢI Ý: Cache > 500MB nên xem xét xoá nếu không dùng thường xuyên."

echo "✅ Hoàn tất kiểm tra!"
