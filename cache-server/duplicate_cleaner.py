#!/usr/bin/env python3
"""
Script để kiểm tra và dọn dẹp thư mục trùng lặp
Hỗ trợ nhiều tiêu chí để quyết định thư mục nào sẽ được giữ lại
"""

import os
import sys
import argparse
import shutil
from pathlib import Path
from collections import defaultdict
from datetime import datetime

class DuplicateCleaner:
    def __init__(self, search_path, dry_run=True, keep_criteria='largest'):
        self.search_path = Path(search_path)
        self.dry_run = dry_run
        self.keep_criteria = keep_criteria  # 'largest', 'newest', 'oldest', 'first_found'
        
    def get_folder_size(self, folder_path):
        """T<PERSON>h kích thước thư mục"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(folder_path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except (<PERSON><PERSON><PERSON><PERSON>, FileNotFoundError):
                        pass
        except (OSError, PermissionError):
            pass
        return total_size
    
    def get_folder_mtime(self, folder_path):
        """<PERSON><PERSON>y thời gian sửa đổi cuối cùng của thư mục"""
        try:
            return os.path.getmtime(folder_path)
        except OSError:
            return 0
    
    def find_duplicates(self):
        """Tìm tất cả thư mục trùng lặp"""
        folder_groups = defaultdict(list)
        
        print(f"🔍 Đang quét thư mục: {self.search_path}")
        
        # Tìm tất cả thư mục
        for folder_path in self.search_path.rglob('*'):
            if folder_path.is_dir():
                folder_name = folder_path.name
                folder_groups[folder_name].append(folder_path)
        
        # Lọc ra những nhóm có nhiều hơn 1 thư mục (trùng lặp)
        duplicates = {name: paths for name, paths in folder_groups.items() if len(paths) > 1}
        
        return duplicates
    
    def choose_folder_to_keep(self, folder_paths):
        """Chọn thư mục nào sẽ được giữ lại dựa trên tiêu chí"""
        if self.keep_criteria == 'largest':
            # Giữ thư mục có kích thước lớn nhất
            return max(folder_paths, key=self.get_folder_size)
        elif self.keep_criteria == 'newest':
            # Giữ thư mục mới nhất
            return max(folder_paths, key=self.get_folder_mtime)
        elif self.keep_criteria == 'oldest':
            # Giữ thư mục cũ nhất
            return min(folder_paths, key=self.get_folder_mtime)
        elif self.keep_criteria == 'first_found':
            # Giữ thư mục đầu tiên trong danh sách
            return folder_paths[0]
        else:
            return folder_paths[0]
    
    def format_size(self, size_bytes):
        """Chuyển đổi bytes thành định dạng dễ đọc"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"
    
    def format_time(self, timestamp):
        """Chuyển đổi timestamp thành định dạng dễ đọc"""
        return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
    
    def clean_duplicates(self, interactive=True):
        """Thực hiện dọn dẹp thư mục trùng lặp"""
        duplicates = self.find_duplicates()
        
        if not duplicates:
            print("✅ Không tìm thấy thư mục trùng lặp nào!")
            return
        
        print(f"\n📋 Tìm thấy {len(duplicates)} nhóm thư mục trùng lặp:")
        print("=" * 60)
        
        total_removed = 0
        total_space_saved = 0
        
        for folder_name, folder_paths in duplicates.items():
            print(f"\n📁 Thư mục trùng: {folder_name}")
            print("-" * 40)
            
            # Thu thập thông tin về từng thư mục
            folder_info = []
            for path in folder_paths:
                size = self.get_folder_size(path)
                mtime = self.get_folder_mtime(path)
                folder_info.append({
                    'path': path,
                    'size': size,
                    'mtime': mtime
                })
            
            # Hiển thị thông tin chi tiết
            for i, info in enumerate(folder_info, 1):
                print(f"  {i}. {info['path']}")
                print(f"     📏 Kích thước: {self.format_size(info['size'])}")
                print(f"     📅 Sửa đổi: {self.format_time(info['mtime'])}")
            
            # Chọn thư mục để giữ lại
            keep_folder_path = self.choose_folder_to_keep(folder_paths)
            folders_to_remove = [path for path in folder_paths if path != keep_folder_path]
            
            print(f"\n🔒 Sẽ giữ lại: {keep_folder_path}")
            print(f"🗑️  Sẽ xóa {len(folders_to_remove)} thư mục:")
            
            for path in folders_to_remove:
                size = self.get_folder_size(path)
                print(f"   - {path} ({self.format_size(size)})")
                total_space_saved += size
            
            # Xác nhận và thực hiện xóa
            if interactive and not self.dry_run:
                confirm = input(f"\n❓ Xóa {len(folders_to_remove)} thư mục trùng lặp? (y/N): ")
                if confirm.lower() != 'y':
                    print("⏭️  Bỏ qua nhóm này")
                    continue
            
            # Thực hiện xóa
            if not self.dry_run:
                for path in folders_to_remove:
                    try:
                        shutil.rmtree(path)
                        print(f"✅ Đã xóa: {path}")
                        total_removed += 1
                    except Exception as e:
                        print(f"❌ Lỗi khi xóa {path}: {e}")
            else:
                print(f"🔍 [DRY RUN] Sẽ xóa {len(folders_to_remove)} thư mục")
                total_removed += len(folders_to_remove)
        
        print("\n" + "=" * 60)
        if self.dry_run:
            print(f"🔍 [DRY RUN] Tổng kết:")
            print(f"   📁 Sẽ xóa: {total_removed} thư mục")
            print(f"   💾 Sẽ tiết kiệm: {self.format_size(total_space_saved)}")
            print(f"\n💡 Để thực sự xóa, chạy lại với tham số --execute")
        else:
            print(f"✅ Hoàn thành!")
            print(f"   📁 Đã xóa: {total_removed} thư mục")
            print(f"   💾 Đã tiết kiệm: {self.format_size(total_space_saved)}")

def main():
    parser = argparse.ArgumentParser(description='Kiểm tra và dọn dẹp thư mục trùng lặp')
    parser.add_argument('path', nargs='?', default='/Users/<USER>/Documents/', 
                       help='Đường dẫn để tìm kiếm (mặc định: /Users/<USER>/Documents/)')
    parser.add_argument('--execute', action='store_true', 
                       help='Thực sự xóa thư mục (mặc định chỉ hiển thị)')
    parser.add_argument('--keep', choices=['largest', 'newest', 'oldest', 'first_found'],
                       default='largest', help='Tiêu chí chọn thư mục để giữ lại')
    parser.add_argument('--non-interactive', action='store_true',
                       help='Không hỏi xác nhận khi xóa')
    
    args = parser.parse_args()
    
    # Kiểm tra đường dẫn
    if not os.path.exists(args.path):
        print(f"❌ Đường dẫn không tồn tại: {args.path}")
        sys.exit(1)
    
    # Tạo cleaner và chạy
    cleaner = DuplicateCleaner(
        search_path=args.path,
        dry_run=not args.execute,
        keep_criteria=args.keep
    )
    
    try:
        cleaner.clean_duplicates(interactive=not args.non_interactive)
    except KeyboardInterrupt:
        print("\n\n⏹️  Đã hủy bởi người dùng")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Lỗi: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
