#!/usr/bin/env python3
"""
macOS Development Environment Cleaner
Phân tích và dọn dẹp bộ nhớ cho môi trường phát triển macOS
Hỗ trợ: Java, Node.js, iOS, Android, và các cache hệ thống
"""

import os
import sys
import json
import shutil
import subprocess
import argparse
from pathlib import Path
from datetime import datetime, timedelta
from collections import defaultdict

class MacOSDevCleaner:
    def __init__(self, dry_run=True, aggressive=False):
        self.dry_run = dry_run
        self.aggressive = aggressive
        self.home = Path.home()
        self.total_space_saved = 0
        self.cleaned_items = []
        
        # Đ<PERSON>nh nghĩa các đường dẫn cache và temp
        self.cache_paths = {
            # macOS System Cache
            'macos_user_cache': self.home / 'Library/Caches',
            'macos_logs': self.home / 'Library/Logs',
            'macos_temp': Path('/tmp'),
            'macos_trash': self.home / '.Trash',
            
            # Development Tools
            'xcode_cache': self.home / 'Library/Developer/Xcode/DerivedData',
            'xcode_archives': self.home / 'Library/Developer/Xcode/Archives',
            'xcode_logs': self.home / 'Library/Developer/Xcode/iOS DeviceSupport',
            'simulator_cache': self.home / 'Library/Developer/CoreSimulator/Caches',
            
            # Java
            'java_cache': self.home / '.cache/JetBrains',
            'maven_cache': self.home / '.m2/repository',
            'gradle_cache': self.home / '.gradle/caches',
            'gradle_daemon': self.home / '.gradle/daemon',
            
            # Node.js
            'npm_cache': self.home / '.npm/_cacache',
            'yarn_cache': self.home / '.yarn/cache',
            'node_modules_global': self.home / '.npm-global/lib/node_modules',
            'pnpm_cache': self.home / '.pnpm-store',
            
            # Android
            'android_cache': self.home / '.android/cache',
            'android_avd_cache': self.home / '.android/avd',
            'gradle_android': self.home / '.gradle/caches/modules-2/files-2.1',
            
            # Other Development
            'docker_cache': self.home / '.docker',
            'pip_cache': self.home / '.cache/pip',
            'homebrew_cache': Path('/usr/local/var/homebrew/cache'),
            'cocoapods_cache': self.home / '.cocoapods/repos',
        }
        
        # Các thư mục build artifacts có thể xóa an toàn
        self.build_artifacts = [
            'node_modules',
            'build',
            'dist',
            'target',
            '.gradle',
            'DerivedData',
            '.next',
            '.nuxt',
            'coverage',
            '.nyc_output',
            '__pycache__',
            '.pytest_cache',
            '.tox',
            'vendor/bundle'
        ]
        
        # Các extension file tạm có thể xóa
        self.temp_extensions = [
            '.log', '.tmp', '.temp', '.cache', '.bak', '.swp', '.swo',
            '.DS_Store', '.localized', 'Thumbs.db', '.pid'
        ]

    def format_size(self, size_bytes):
        """Chuyển đổi bytes thành định dạng dễ đọc"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"

    def get_folder_size(self, folder_path):
        """Tính kích thước thư mục"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(folder_path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except (OSError, FileNotFoundError):
                        pass
        except (OSError, PermissionError):
            pass
        return total_size

    def is_safe_to_delete(self, path, category):
        """Kiểm tra xem có an toàn để xóa không"""
        path = Path(path)
        
        # Không xóa nếu đang được sử dụng
        if self.is_path_in_use(path):
            return False
            
        # Kiểm tra theo category
        if category == 'cache':
            return True
        elif category == 'logs':
            # Chỉ xóa log cũ hơn 7 ngày
            try:
                mtime = datetime.fromtimestamp(path.stat().st_mtime)
                return datetime.now() - mtime > timedelta(days=7)
            except:
                return False
        elif category == 'build_artifacts':
            # Kiểm tra xem có phải project đang active không
            return not self.is_active_project(path.parent)
        
        return False

    def is_path_in_use(self, path):
        """Kiểm tra xem đường dẫn có đang được sử dụng không"""
        try:
            # Kiểm tra process đang chạy
            result = subprocess.run(['lsof', '+D', str(path)], 
                                  capture_output=True, text=True, timeout=5)
            return len(result.stdout.strip()) > 0
        except:
            return False

    def is_active_project(self, project_path):
        """Kiểm tra xem có phải project đang active không"""
        project_path = Path(project_path)
        
        # Kiểm tra git activity gần đây
        git_dir = project_path / '.git'
        if git_dir.exists():
            try:
                git_log = git_dir / 'logs/HEAD'
                if git_log.exists():
                    mtime = datetime.fromtimestamp(git_log.stat().st_mtime)
                    if datetime.now() - mtime < timedelta(days=30):
                        return True
            except:
                pass
        
        # Kiểm tra file được sửa đổi gần đây
        try:
            for file_path in project_path.rglob('*'):
                if file_path.is_file():
                    mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if datetime.now() - mtime < timedelta(days=7):
                        return True
        except:
            pass
            
        return False

    def analyze_disk_usage(self):
        """Phân tích sử dụng ổ đĩa"""
        print("🔍 Đang phân tích sử dụng ổ đĩa...")
        
        analysis = {
            'cache_folders': {},
            'build_artifacts': {},
            'temp_files': {},
            'large_files': {},
            'total_cleanable': 0
        }
        
        # Phân tích cache folders
        for name, path in self.cache_paths.items():
            if path.exists():
                size = self.get_folder_size(path)
                if size > 0:
                    analysis['cache_folders'][name] = {
                        'path': str(path),
                        'size': size,
                        'size_human': self.format_size(size)
                    }
                    analysis['total_cleanable'] += size
        
        # Tìm build artifacts trong thư mục phát triển
        dev_dirs = [
            self.home / 'Documents',
            self.home / 'Desktop',
            self.home / 'Projects',
            self.home / 'Development'
        ]
        
        for dev_dir in dev_dirs:
            if dev_dir.exists():
                self.find_build_artifacts(dev_dir, analysis)
        
        return analysis

    def find_build_artifacts(self, search_dir, analysis):
        """Tìm build artifacts trong thư mục"""
        try:
            for item in search_dir.rglob('*'):
                if item.is_dir() and item.name in self.build_artifacts:
                    # Kiểm tra xem có phải project active không
                    if not self.is_active_project(item.parent):
                        size = self.get_folder_size(item)
                        if size > 1024 * 1024:  # Chỉ quan tâm file > 1MB
                            analysis['build_artifacts'][str(item)] = {
                                'size': size,
                                'size_human': self.format_size(size),
                                'project': str(item.parent)
                            }
                            analysis['total_cleanable'] += size
        except (PermissionError, OSError):
            pass

    def clean_cache_folders(self):
        """Dọn dẹp cache folders"""
        print("\n🧹 Dọn dẹp cache folders...")
        
        for name, path in self.cache_paths.items():
            if not path.exists():
                continue
                
            size_before = self.get_folder_size(path)
            if size_before == 0:
                continue
                
            print(f"  📁 {name}: {path}")
            print(f"     📏 Kích thước: {self.format_size(size_before)}")
            
            if self.is_safe_to_delete(path, 'cache'):
                if not self.dry_run:
                    try:
                        if name in ['macos_temp', 'macos_trash']:
                            # Xóa nội dung, không xóa thư mục gốc
                            for item in path.iterdir():
                                if item.is_dir():
                                    shutil.rmtree(item)
                                else:
                                    item.unlink()
                        else:
                            shutil.rmtree(path)
                            path.mkdir(exist_ok=True)
                        
                        self.total_space_saved += size_before
                        self.cleaned_items.append(f"{name}: {self.format_size(size_before)}")
                        print(f"     ✅ Đã dọn dẹp")
                    except Exception as e:
                        print(f"     ❌ Lỗi: {e}")
                else:
                    print(f"     🔍 [DRY RUN] Sẽ dọn dẹp")
                    self.total_space_saved += size_before
            else:
                print(f"     ⚠️  Bỏ qua (đang sử dụng hoặc không an toàn)")

    def clean_build_artifacts(self):
        """Dọn dẹp build artifacts"""
        print("\n🔨 Dọn dẹp build artifacts...")
        
        analysis = self.analyze_disk_usage()
        
        for artifact_path, info in analysis['build_artifacts'].items():
            print(f"  📁 {artifact_path}")
            print(f"     📏 Kích thước: {info['size_human']}")
            print(f"     🏗️  Project: {info['project']}")
            
            if not self.dry_run:
                try:
                    shutil.rmtree(artifact_path)
                    self.total_space_saved += info['size']
                    self.cleaned_items.append(f"Build artifact: {self.format_size(info['size'])}")
                    print(f"     ✅ Đã xóa")
                except Exception as e:
                    print(f"     ❌ Lỗi: {e}")
            else:
                print(f"     🔍 [DRY RUN] Sẽ xóa")

    def clean_temp_files(self):
        """Dọn dẹp temp files"""
        print("\n🗂️  Dọn dẹp temp files...")
        
        search_dirs = [self.home / 'Documents', self.home / 'Desktop', self.home / 'Downloads']
        
        for search_dir in search_dirs:
            if not search_dir.exists():
                continue
                
            print(f"  📂 Quét: {search_dir}")
            temp_files = []
            
            try:
                for file_path in search_dir.rglob('*'):
                    if file_path.is_file() and file_path.suffix in self.temp_extensions:
                        temp_files.append(file_path)
            except (PermissionError, OSError):
                continue
            
            if temp_files:
                total_size = sum(f.stat().st_size for f in temp_files if f.exists())
                print(f"     📄 Tìm thấy {len(temp_files)} file temp ({self.format_size(total_size)})")
                
                if not self.dry_run:
                    removed = 0
                    for file_path in temp_files:
                        try:
                            file_path.unlink()
                            removed += 1
                        except:
                            pass
                    print(f"     ✅ Đã xóa {removed} files")
                    self.total_space_saved += total_size
                else:
                    print(f"     🔍 [DRY RUN] Sẽ xóa {len(temp_files)} files")

    def run_system_cleanup(self):
        """Chạy các lệnh dọn dẹp hệ thống"""
        print("\n🖥️  Chạy system cleanup...")
        
        commands = [
            ('Purge memory', ['sudo', 'purge']),
            ('Clear DNS cache', ['sudo', 'dscacheutil', '-flushcache']),
            ('Rebuild Launch Services', ['sudo', '/System/Library/Frameworks/CoreServices.framework/Frameworks/LaunchServices.framework/Support/lsregister', '-kill', '-r', '-domain', 'local', '-domain', 'system', '-domain', 'user']),
        ]
        
        for desc, cmd in commands:
            print(f"  🔧 {desc}")
            if not self.dry_run:
                try:
                    subprocess.run(cmd, check=True, capture_output=True)
                    print(f"     ✅ Hoàn thành")
                except subprocess.CalledProcessError as e:
                    print(f"     ⚠️  Lỗi: {e}")
                except FileNotFoundError:
                    print(f"     ⚠️  Lệnh không tìm thấy")
            else:
                print(f"     🔍 [DRY RUN] Sẽ chạy: {' '.join(cmd)}")

    def generate_report(self):
        """Tạo báo cáo tổng kết"""
        print("\n" + "="*60)
        print("📊 BÁO CÁO TỔNG KẾT")
        print("="*60)
        
        if self.dry_run:
            print("🔍 [DRY RUN MODE] - Không có file nào bị xóa thực sự")
        
        print(f"💾 Tổng dung lượng có thể tiết kiệm: {self.format_size(self.total_space_saved)}")
        
        if self.cleaned_items:
            print(f"📋 Chi tiết ({len(self.cleaned_items)} items):")
            for item in self.cleaned_items[:10]:  # Hiển thị 10 items đầu
                print(f"   • {item}")
            if len(self.cleaned_items) > 10:
                print(f"   ... và {len(self.cleaned_items) - 10} items khác")
        
        # Đề xuất
        print(f"\n💡 ĐỀ XUẤT:")
        if self.dry_run:
            print("   • Chạy lại với --execute để thực sự dọn dẹp")
        print("   • Định kỳ chạy script này hàng tuần")
        print("   • Backup dữ liệu quan trọng trước khi dọn dẹp")
        print("   • Sử dụng --aggressive để dọn dẹp sâu hơn (cẩn thận!)")

def main():
    parser = argparse.ArgumentParser(description='macOS Development Environment Cleaner')
    parser.add_argument('--execute', action='store_true', 
                       help='Thực sự xóa files (mặc định chỉ phân tích)')
    parser.add_argument('--aggressive', action='store_true',
                       help='Dọn dẹp aggressive hơn (bao gồm cả project có vẻ inactive)')
    parser.add_argument('--cache-only', action='store_true',
                       help='Chỉ dọn dẹp cache, không động đến build artifacts')
    parser.add_argument('--no-system', action='store_true',
                       help='Không chạy system cleanup commands')
    
    args = parser.parse_args()
    
    print("🍎 macOS Development Environment Cleaner")
    print("="*50)
    
    if not args.execute:
        print("⚠️  CHẠY Ở CHẾ ĐỘ PHÂN TÍCH - Không có file nào bị xóa")
        print("   Sử dụng --execute để thực sự dọn dẹp")
    
    cleaner = MacOSDevCleaner(dry_run=not args.execute, aggressive=args.aggressive)
    
    try:
        # Phân tích trước
        analysis = cleaner.analyze_disk_usage()
        print(f"\n📊 Tổng dung lượng có thể dọn dẹp: {cleaner.format_size(analysis['total_cleanable'])}")
        
        # Dọn dẹp cache
        cleaner.clean_cache_folders()
        
        # Dọn dẹp build artifacts (nếu không chỉ cache)
        if not args.cache_only:
            cleaner.clean_build_artifacts()
            cleaner.clean_temp_files()
        
        # System cleanup (nếu không bị disable)
        if not args.no_system and args.execute:
            cleaner.run_system_cleanup()
        
        # Báo cáo
        cleaner.generate_report()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Đã hủy bởi người dùng")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Lỗi: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
