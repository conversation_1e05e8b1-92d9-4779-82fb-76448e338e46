# 🔧 HỆ THỐNG QUẢN LÝ HỌC VIÊN VÕ THUẬT - TECHNICAL SPECIFICATIONS
## Chi tiết kỹ thuật và Implementation Guidelines

---

## 🏗️ **SYSTEM ARCHITECTURE OVERVIEW**

### 📊 **High-Level Architecture**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile Apps   │    │   Web Portal    │    │  Public Portal  │
│  (React Native) │    │   (Next.js)     │    │   (Next.js)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API Gateway   │
                    │   (Rust/Axum)   │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Auth Service   │    │ Student Service │    │  Exam Service   │
│  (Rust/Axum)    │    │  (Rust/Axum)    │    │  (Rust/Axum)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   PostgreSQL    │
                    │   + Redis Cache │
                    └─────────────────┘
```

### 🔄 **Microservices Communication**
```rust
// Service-to-Service Communication Pattern
use axum::http::HeaderMap;
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize)]
pub struct ServiceRequest<T> {
    pub correlation_id: String,
    pub user_id: Option<String>,
    pub org_id: Option<String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub payload: T,
}

#[derive(Serialize, Deserialize)]
pub struct ServiceResponse<T> {
    pub correlation_id: String,
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<ServiceError>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

// Inter-service HTTP client
pub struct ServiceClient {
    client: reqwest::Client,
    base_url: String,
    service_token: String,
}

impl ServiceClient {
    pub async fn call<Req, Res>(&self, endpoint: &str, request: ServiceRequest<Req>)
        -> Result<ServiceResponse<Res>, ServiceError>
    where
        Req: Serialize,
        Res: for<'de> Deserialize<'de>,
    {
        let response = self.client
            .post(&format!("{}/{}", self.base_url, endpoint))
            .header("Authorization", format!("Bearer {}", self.service_token))
            .header("Content-Type", "application/json")
            .json(&request)
            .send()
            .await?;

        let service_response: ServiceResponse<Res> = response.json().await?;
        Ok(service_response)
    }
}
```

---

## 🔐 **AUTHENTICATION & AUTHORIZATION**

### 🎫 **JWT Token Structure**
```rust
use jsonwebtoken::{decode, encode, DecodingKey, EncodingKey, Header, Validation};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,                    // User ID
    pub org_id: String,                 // Organization ID
    pub org_path: Vec<String>,          // Organization hierarchy path
    pub role: String,                   // User role
    pub permissions: Vec<String>,       // Specific permissions
    pub martial_arts: Vec<String>,      // Accessible martial arts
    pub exp: i64,                       // Expiration time
    pub iat: i64,                       // Issued at
    pub jti: String,                    // JWT ID for revocation
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RefreshTokenClaims {
    pub sub: String,
    pub token_family: String,           // For token rotation
    pub exp: i64,
    pub iat: i64,
}

// Permission-based authorization middleware
pub async fn require_permission(
    permission: &str,
    claims: &Claims,
) -> Result<(), AuthError> {
    if claims.permissions.contains(&permission.to_string()) {
        Ok(())
    } else {
        Err(AuthError::InsufficientPermissions)
    }
}

// Organization hierarchy access control
pub fn can_access_organization(user_org_path: &[String], target_org_id: &str) -> bool {
    // User can access their own organization and all sub-organizations
    // Implementation depends on organization hierarchy lookup
    true // Simplified
}
```

### 🔒 **Role-Based Access Control (RBAC)**
```rust
use std::collections::HashMap;

#[derive(Debug, Clone)]
pub struct Permission {
    pub resource: String,
    pub action: String,
    pub scope: PermissionScope,
}

#[derive(Debug, Clone)]
pub enum PermissionScope {
    Own,                    // Own records only
    Organization,           // Same organization
    Hierarchy,             // Organization + sub-organizations
    System,                // System-wide access
}

pub struct RoleManager {
    roles: HashMap<String, Vec<Permission>>,
}

impl RoleManager {
    pub fn new() -> Self {
        let mut roles = HashMap::new();

        // Define role permissions
        roles.insert("DEPARTMENT_ADMIN".to_string(), vec![
            Permission {
                resource: "students".to_string(),
                action: "read".to_string(),
                scope: PermissionScope::Organization,
            },
            Permission {
                resource: "students".to_string(),
                action: "write".to_string(),
                scope: PermissionScope::Organization,
            },
            Permission {
                resource: "exams".to_string(),
                action: "create".to_string(),
                scope: PermissionScope::Organization,
            },
        ]);

        roles.insert("PROVINCE_ADMIN".to_string(), vec![
            Permission {
                resource: "students".to_string(),
                action: "read".to_string(),
                scope: PermissionScope::Hierarchy,
            },
            Permission {
                resource: "organizations".to_string(),
                action: "approve".to_string(),
                scope: PermissionScope::Hierarchy,
            },
        ]);

        Self { roles }
    }

    pub fn get_permissions(&self, role: &str) -> Vec<Permission> {
        self.roles.get(role).cloned().unwrap_or_default()
    }
}
```

---

## 📊 **DATABASE DESIGN PATTERNS**

### 🔄 **Repository Pattern Implementation**
```rust
use async_trait::async_trait;
use sqlx::{PgPool, Row};
use uuid::Uuid;

#[async_trait]
pub trait Repository<T> {
    async fn find_by_id(&self, id: Uuid) -> Result<Option<T>, DatabaseError>;
    async fn find_all(&self, filters: &HashMap<String, String>) -> Result<Vec<T>, DatabaseError>;
    async fn create(&self, entity: &T) -> Result<T, DatabaseError>;
    async fn update(&self, id: Uuid, entity: &T) -> Result<T, DatabaseError>;
    async fn delete(&self, id: Uuid) -> Result<(), DatabaseError>;
}

pub struct StudentRepository {
    pool: PgPool,
}

#[async_trait]
impl Repository<Student> for StudentRepository {
    async fn find_by_id(&self, id: Uuid) -> Result<Option<Student>, DatabaseError> {
        let student = sqlx::query_as!(
            Student,
            r#"
            SELECT id, student_code, full_name, date_of_birth, gender as "gender: Gender",
                   address, phone, email, org_id, martial_art_id, current_rank_id,
                   is_active, created_at, updated_at
            FROM students
            WHERE id = $1 AND is_active = true
            "#,
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(student)
    }

    async fn find_all(&self, filters: &HashMap<String, String>) -> Result<Vec<Student>, DatabaseError> {
        let mut query = QueryBuilder::new("SELECT * FROM students WHERE is_active = true");

        if let Some(org_id) = filters.get("org_id") {
            query.push(" AND org_id = ");
            query.push_bind(Uuid::parse_str(org_id)?);
        }

        if let Some(martial_art_id) = filters.get("martial_art_id") {
            query.push(" AND martial_art_id = ");
            query.push_bind(Uuid::parse_str(martial_art_id)?);
        }

        if let Some(search) = filters.get("search") {
            query.push(" AND (full_name ILIKE ");
            query.push_bind(format!("%{}%", search));
            query.push(" OR student_code ILIKE ");
            query.push_bind(format!("%{}%", search));
            query.push(")");
        }

        query.push(" ORDER BY full_name");

        let students = query
            .build_query_as::<Student>()
            .fetch_all(&self.pool)
            .await?;

        Ok(students)
    }

    // ... other methods
}
```

### 🔍 **Advanced Query Patterns**
```rust
// Complex query with joins and aggregations
pub async fn get_student_statistics(
    pool: &PgPool,
    org_id: Uuid,
    date_range: (chrono::NaiveDate, chrono::NaiveDate),
) -> Result<StudentStatistics, DatabaseError> {
    let stats = sqlx::query!(
        r#"
        SELECT
            COUNT(*) as total_students,
            COUNT(CASE WHEN s.gender = 'MALE' THEN 1 END) as male_count,
            COUNT(CASE WHEN s.gender = 'FEMALE' THEN 1 END) as female_count,
            AVG(EXTRACT(YEAR FROM AGE(s.date_of_birth))) as avg_age,
            COUNT(DISTINCT s.martial_art_id) as martial_arts_count,
            COUNT(CASE WHEN s.enrolled_at >= $2 AND s.enrolled_at <= $3 THEN 1 END) as new_enrollments
        FROM students s
        WHERE s.org_id = $1 AND s.is_active = true
        "#,
        org_id,
        date_range.0,
        date_range.1
    )
    .fetch_one(pool)
    .await?;

    Ok(StudentStatistics {
        total_students: stats.total_students.unwrap_or(0) as u32,
        male_count: stats.male_count.unwrap_or(0) as u32,
        female_count: stats.female_count.unwrap_or(0) as u32,
        avg_age: stats.avg_age.unwrap_or(0.0) as f32,
        martial_arts_count: stats.martial_arts_count.unwrap_or(0) as u32,
        new_enrollments: stats.new_enrollments.unwrap_or(0) as u32,
    })
}

// Hierarchical organization query
pub async fn get_organization_tree(
    pool: &PgPool,
    root_org_id: Option<Uuid>,
) -> Result<Vec<OrganizationNode>, DatabaseError> {
    let organizations = sqlx::query!(
        r#"
        WITH RECURSIVE org_tree AS (
            SELECT id, name, code, level, parent_id, 0 as depth,
                   ARRAY[id] as path, ARRAY[name] as name_path
            FROM organizations
            WHERE parent_id IS NULL OR parent_id = $1

            UNION ALL

            SELECT o.id, o.name, o.code, o.level, o.parent_id, ot.depth + 1,
                   ot.path || o.id, ot.name_path || o.name
            FROM organizations o
            JOIN org_tree ot ON o.parent_id = ot.id
            WHERE ot.depth < 10  -- Prevent infinite recursion
        )
        SELECT ot.*,
               COUNT(s.id) as student_count,
               COUNT(DISTINCT s.martial_art_id) as martial_arts_count
        FROM org_tree ot
        LEFT JOIN students s ON s.org_id = ot.id AND s.is_active = true
        GROUP BY ot.id, ot.name, ot.code, ot.level, ot.parent_id, ot.depth, ot.path, ot.name_path
        ORDER BY ot.depth, ot.name
        "#,
        root_org_id
    )
    .fetch_all(pool)
    .await?;

    // Convert to tree structure
    let mut nodes = Vec::new();
    for org in organizations {
        nodes.push(OrganizationNode {
            id: org.id,
            name: org.name,
            code: org.code,
            level: org.level,
            parent_id: org.parent_id,
            depth: org.depth as u32,
            student_count: org.student_count.unwrap_or(0) as u32,
            martial_arts_count: org.martial_arts_count.unwrap_or(0) as u32,
            children: Vec::new(),
        });
    }

    Ok(build_tree(nodes))
}
```

---

## 🔄 **STATE MACHINE IMPLEMENTATION**

### 📋 **Transfer Request Workflow**
```rust
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TransferStatus {
    Pending,
    ApprovedByFrom,
    ApprovedByTo,
    ApprovedByParent,
    Completed,
    Rejected,
}

#[derive(Debug, Clone)]
pub struct TransferStateMachine {
    transitions: HashMap<TransferStatus, Vec<TransferStatus>>,
}

impl TransferStateMachine {
    pub fn new() -> Self {
        let mut transitions = HashMap::new();

        transitions.insert(TransferStatus::Pending, vec![
            TransferStatus::ApprovedByFrom,
            TransferStatus::Rejected,
        ]);

        transitions.insert(TransferStatus::ApprovedByFrom, vec![
            TransferStatus::ApprovedByTo,
            TransferStatus::Rejected,
        ]);

        transitions.insert(TransferStatus::ApprovedByTo, vec![
            TransferStatus::ApprovedByParent,
            TransferStatus::Completed,
            TransferStatus::Rejected,
        ]);

        transitions.insert(TransferStatus::ApprovedByParent, vec![
            TransferStatus::Completed,
            TransferStatus::Rejected,
        ]);

        Self { transitions }
    }

    pub fn can_transition(&self, from: &TransferStatus, to: &TransferStatus) -> bool {
        self.transitions
            .get(from)
            .map(|allowed| allowed.contains(to))
            .unwrap_or(false)
    }

    pub fn get_allowed_transitions(&self, from: &TransferStatus) -> Vec<TransferStatus> {
        self.transitions.get(from).cloned().unwrap_or_default()
    }
}

#[derive(Debug)]
pub struct TransferWorkflow {
    state_machine: TransferStateMachine,
    pool: PgPool,
}

impl TransferWorkflow {
    pub async fn approve_by_from(
        &self,
        transfer_id: Uuid,
        approver_id: Uuid,
        notes: Option<String>,
    ) -> Result<TransferRequest, WorkflowError> {
        let mut tx = self.pool.begin().await?;

        // Get current transfer
        let transfer = self.get_transfer(&mut tx, transfer_id).await?;

        // Validate state transition
        if !self.state_machine.can_transition(&transfer.status, &TransferStatus::ApprovedByFrom) {
            return Err(WorkflowError::InvalidTransition {
                from: transfer.status,
                to: TransferStatus::ApprovedByFrom,
            });
        }

        // Validate approver permissions
        self.validate_from_org_approver(&mut tx, &transfer, approver_id).await?;

        // Update transfer status
        let updated_transfer = sqlx::query_as!(
            TransferRequest,
            r#"
            UPDATE transfer_requests
            SET status = 'APPROVED_BY_FROM',
                approved_by_from = $2,
                approved_by_from_at = NOW(),
                approved_by_from_notes = $3,
                updated_at = NOW()
            WHERE id = $1
            RETURNING *
            "#,
            transfer_id,
            approver_id,
            notes
        )
        .fetch_one(&mut tx)
        .await?;

        // Send notification to destination organization
        self.send_approval_notification(&mut tx, &updated_transfer, NotificationType::ToOrgApprovalNeeded).await?;

        // Log activity
        self.log_transfer_activity(&mut tx, transfer_id, "APPROVED_BY_FROM", approver_id).await?;

        tx.commit().await?;
        Ok(updated_transfer)
    }

    async fn validate_from_org_approver(
        &self,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
        transfer: &TransferRequest,
        approver_id: Uuid,
    ) -> Result<(), WorkflowError> {
        let approver = sqlx::query!(
            "SELECT role, org_id FROM users WHERE id = $1",
            approver_id
        )
        .fetch_optional(tx)
        .await?
        .ok_or(WorkflowError::ApproverNotFound)?;

        // Check if approver belongs to the source organization or its parent
        let can_approve = sqlx::query!(
            r#"
            WITH RECURSIVE org_hierarchy AS (
                SELECT id, parent_id, 0 as level FROM organizations WHERE id = $1
                UNION ALL
                SELECT o.id, o.parent_id, oh.level + 1
                FROM organizations o
                JOIN org_hierarchy oh ON o.id = oh.parent_id
                WHERE oh.level < 5
            )
            SELECT EXISTS(
                SELECT 1 FROM org_hierarchy WHERE id = $2
            ) as can_approve
            "#,
            transfer.from_org_id,
            approver.org_id
        )
        .fetch_one(tx)
        .await?;

        if !can_approve.can_approve.unwrap_or(false) {
            return Err(WorkflowError::InsufficientPermissions);
        }

        Ok(())
    }
}
```

---

## 📱 **MOBILE APP ARCHITECTURE**

### 🏗️ **React Native Project Structure**
```typescript
// src/types/index.ts - Shared TypeScript types
export interface Student {
  id: string;
  studentCode: string;
  fullName: string;
  dateOfBirth: string;
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  phone?: string;
  email?: string;
  organizationId: string;
  martialArtId: string;
  currentRankId?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Organization {
  id: string;
  name: string;
  code: string;
  level: 'NATIONAL' | 'PROVINCE' | 'DISTRICT' | 'DEPARTMENT';
  parentId?: string;
  address?: string;
  phone?: string;
  email?: string;
}

export interface AttendanceSession {
  id: string;
  title: string;
  organizationId: string;
  instructorId: string;
  sessionDate: string;
  startTime: string;
  endTime: string;
  location?: string;
  qrCode?: string;
  isActive: boolean;
}

// src/stores/authStore.ts - Zustand authentication store
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ReactNativeBiometrics } from 'react-native-biometrics';

interface User {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: string;
  organizationId: string;
  organization?: Organization;
}

interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  biometricEnabled: boolean;
  isAuthenticated: boolean;

  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  refreshAccessToken: () => Promise<void>;
  enableBiometric: () => Promise<void>;
  loginWithBiometric: () => Promise<void>;
  setUser: (user: User) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      accessToken: null,
      refreshToken: null,
      biometricEnabled: false,
      isAuthenticated: false,

      login: async (credentials) => {
        try {
          const response = await authApi.login(credentials);

          set({
            user: response.user,
            accessToken: response.accessToken,
            refreshToken: response.refreshToken,
            isAuthenticated: true,
          });

          // Store tokens securely
          await AsyncStorage.setItem('accessToken', response.accessToken);
          await AsyncStorage.setItem('refreshToken', response.refreshToken);

        } catch (error) {
          throw new Error('Login failed');
        }
      },

      logout: async () => {
        try {
          const { refreshToken } = get();
          if (refreshToken) {
            await authApi.logout(refreshToken);
          }
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set({
            user: null,
            accessToken: null,
            refreshToken: null,
            isAuthenticated: false,
          });

          await AsyncStorage.multiRemove(['accessToken', 'refreshToken']);
        }
      },

      enableBiometric: async () => {
        try {
          const rnBiometrics = new ReactNativeBiometrics();
          const { available, biometryType } = await rnBiometrics.isSensorAvailable();

          if (available) {
            const { success } = await rnBiometrics.simplePrompt({
              promptMessage: 'Xác thực sinh trắc học để kích hoạt đăng nhập nhanh',
            });

            if (success) {
              const { refreshToken } = get();
              await AsyncStorage.setItem('biometricToken', refreshToken || '');
              set({ biometricEnabled: true });
            }
          }
        } catch (error) {
          throw new Error('Biometric setup failed');
        }
      },

      loginWithBiometric: async () => {
        try {
          const rnBiometrics = new ReactNativeBiometrics();
          const { success } = await rnBiometrics.simplePrompt({
            promptMessage: 'Đăng nhập bằng sinh trắc học',
          });

          if (success) {
            const biometricToken = await AsyncStorage.getItem('biometricToken');
            if (biometricToken) {
              const response = await authApi.refreshToken(biometricToken);
              set({
                user: response.user,
                accessToken: response.accessToken,
                refreshToken: response.refreshToken,
                isAuthenticated: true,
              });
            }
          }
        } catch (error) {
          throw new Error('Biometric login failed');
        }
      },

      refreshAccessToken: async () => {
        try {
          const { refreshToken } = get();
          if (!refreshToken) throw new Error('No refresh token');

          const response = await authApi.refreshToken(refreshToken);
          set({
            accessToken: response.accessToken,
            refreshToken: response.refreshToken,
          });

          await AsyncStorage.setItem('accessToken', response.accessToken);
          await AsyncStorage.setItem('refreshToken', response.refreshToken);
        } catch (error) {
          // Refresh failed, logout user
          get().logout();
          throw error;
        }
      },

      setUser: (user) => set({ user }),
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        biometricEnabled: state.biometricEnabled,
      }),
    }
  )
);
```

### 📷 **QR Code Scanner Implementation**
```typescript
// src/screens/QRScannerScreen.tsx
import React, { useState, useEffect } from 'react';
import { View, Text, Alert, Vibration, StyleSheet } from 'react-native';
import { RNCamera } from 'react-native-camera';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useAuthStore } from '../stores/authStore';
import { attendanceApi } from '../services/api';

interface QRScannerScreenProps {
  route: {
    params: {
      sessionId: string;
    };
  };
}

export function QRScannerScreen({ route }: QRScannerScreenProps) {
  const { sessionId } = route.params;
  const [scanning, setScanning] = useState(true);
  const [lastScan, setLastScan] = useState<string>('');
  const { user } = useAuthStore();

  // Get session details
  const { data: session } = useQuery({
    queryKey: ['attendance-session', sessionId],
    queryFn: () => attendanceApi.getSession(sessionId),
  });

  // Mark attendance mutation
  const markAttendanceMutation = useMutation({
    mutationFn: attendanceApi.markAttendance,
    onSuccess: (data) => {
      Alert.alert(
        'Thành công! ✅',
        `Đã điểm danh cho ${data.studentName}\nThời gian: ${new Date().toLocaleTimeString()}`,
        [{ text: 'OK', onPress: () => setScanning(true) }]
      );
      Vibration.vibrate(200); // Success vibration
    },
    onError: (error: any) => {
      Alert.alert(
        'Lỗi ❌',
        error.message || 'Không thể điểm danh. Vui lòng thử lại.',
        [{ text: 'OK', onPress: () => setScanning(true) }]
      );
      Vibration.vibrate([100, 100, 100]); // Error vibration pattern
    },
  });

  const onBarCodeRead = (scanResult: any) => {
    if (!scanning || scanResult.data === lastScan) return;

    setScanning(false);
    setLastScan(scanResult.data);

    try {
      const qrData = JSON.parse(scanResult.data);

      if (qrData.type === 'student_attendance' && qrData.studentId) {
        markAttendanceMutation.mutate({
          sessionId,
          studentId: qrData.studentId,
          timestamp: new Date().toISOString(),
          markedBy: user?.id,
          method: 'QR_CODE',
        });
      } else {
        Alert.alert('QR Code không hợp lệ', 'Vui lòng quét QR code trên thẻ học viên.');
        setScanning(true);
      }
    } catch (error) {
      Alert.alert('QR Code không hợp lệ', 'Không thể đọc được QR code.');
      setScanning(true);
    }
  };

  return (
    <View style={styles.container}>
      <RNCamera
        style={styles.camera}
        onBarCodeRead={onBarCodeRead}
        barCodeTypes={[RNCamera.Constants.BarCodeType.qr]}
        captureAudio={false}
      >
        <View style={styles.overlay}>
          <View style={styles.header}>
            <Text style={styles.title}>Điểm danh QR Code</Text>
            <Text style={styles.subtitle}>
              {session?.title || 'Đang tải...'}
            </Text>
          </View>

          <View style={styles.scanArea}>
            <View style={styles.scanFrame} />
            <Text style={styles.instruction}>
              Đưa QR code vào khung để quét
            </Text>
          </View>

          <View style={styles.footer}>
            <Text style={styles.info}>
              📅 {session?.sessionDate} | ⏰ {session?.startTime}
            </Text>
            <Text style={styles.info}>
              📍 {session?.location || 'Không xác định'}
            </Text>
          </View>
        </View>
      </RNCamera>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'space-between',
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#ccc',
  },
  scanArea: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrame: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: '#00ff00',
    backgroundColor: 'transparent',
  },
  instruction: {
    marginTop: 20,
    fontSize: 16,
    color: 'white',
    textAlign: 'center',
  },
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  info: {
    fontSize: 14,
    color: '#ccc',
    marginBottom: 4,
  },
});
```

---

## 🌐 **NEXT.JS WEBSITE ARCHITECTURE**

### 🏗️ **App Router Structure**
```typescript
// app/layout.tsx - Root layout with providers
import { Inter } from 'next/font/google';
import { Providers } from './providers';
import { Toaster } from '@/components/ui/toaster';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="vi">
      <body className={inter.className}>
        <Providers>
          {children}
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}

// app/providers.tsx - All providers setup
'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ThemeProvider } from '@/components/theme-provider';
import { AuthProvider } from '@/components/auth-provider';
import { useState } from 'react';

export function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000, // 1 minute
            retry: (failureCount, error: any) => {
              if (error?.status === 401) return false;
              return failureCount < 3;
            },
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        <AuthProvider>
          {children}
        </AuthProvider>
      </ThemeProvider>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}

// app/(dashboard)/layout.tsx - Dashboard layout with sidebar
import { Sidebar } from '@/components/sidebar';
import { Header } from '@/components/header';
import { AuthGuard } from '@/components/auth-guard';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AuthGuard>
      <div className="flex h-screen bg-gray-100">
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Header />
          <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
            {children}
          </main>
        </div>
      </div>
    </AuthGuard>
  );
}
```

### 🔐 **Authentication System**
```typescript
// lib/auth.ts - Authentication utilities
import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { JWT } from 'next-auth/jwt';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        username: { label: 'Username', type: 'text' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          return null;
        }

        try {
          const response = await fetch(`${process.env.API_URL}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              username: credentials.username,
              password: credentials.password,
            }),
          });

          if (!response.ok) {
            return null;
          }

          const data = await response.json();

          return {
            id: data.user.id,
            name: data.user.fullName,
            email: data.user.email,
            role: data.user.role,
            organizationId: data.user.organizationId,
            accessToken: data.accessToken,
            refreshToken: data.refreshToken,
          };
        } catch (error) {
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      if (account && user) {
        return {
          ...token,
          accessToken: user.accessToken,
          refreshToken: user.refreshToken,
          role: user.role,
          organizationId: user.organizationId,
        };
      }

      // Check if access token is expired
      if (token.accessToken) {
        const payload = JSON.parse(atob(token.accessToken.split('.')[1]));
        const isExpired = Date.now() >= payload.exp * 1000;

        if (isExpired && token.refreshToken) {
          try {
            const response = await fetch(`${process.env.API_URL}/auth/refresh`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ refreshToken: token.refreshToken }),
            });

            if (response.ok) {
              const data = await response.json();
              return {
                ...token,
                accessToken: data.accessToken,
                refreshToken: data.refreshToken,
              };
            }
          } catch (error) {
            console.error('Token refresh failed:', error);
          }
        }
      }

      return token;
    },
    async session({ session, token }) {
      return {
        ...session,
        user: {
          ...session.user,
          role: token.role,
          organizationId: token.organizationId,
        },
        accessToken: token.accessToken,
      };
    },
  },
  pages: {
    signIn: '/login',
    error: '/auth/error',
  },
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
  },
};

// components/auth-guard.tsx - Route protection
'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

interface AuthGuardProps {
  children: React.ReactNode;
  requiredRole?: string[];
}

export function AuthGuard({ children, requiredRole }: AuthGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/login');
      return;
    }

    if (requiredRole && !requiredRole.includes(session.user.role)) {
      router.push('/unauthorized');
      return;
    }
  }, [session, status, router, requiredRole]);

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!session) {
    return null;
  }

  if (requiredRole && !requiredRole.includes(session.user.role)) {
    return null;
  }

  return <>{children}</>;
}
```

---

## 📊 **PERFORMANCE OPTIMIZATION**

### ⚡ **Database Optimization**
```sql
-- Performance optimization indexes
CREATE INDEX CONCURRENTLY idx_students_search
ON students USING gin(to_tsvector('english', full_name || ' ' || student_code));

CREATE INDEX CONCURRENTLY idx_students_org_martial_art
ON students(org_id, martial_art_id) WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_attendance_session_date
ON attendance_records(session_id, check_in_time)
WHERE status = 'PRESENT';

-- Partitioning for large tables
CREATE TABLE attendance_records_2024 PARTITION OF attendance_records
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

CREATE TABLE attendance_records_2025 PARTITION OF attendance_records
FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');

-- Materialized views for reporting
CREATE MATERIALIZED VIEW student_statistics AS
SELECT
    o.id as org_id,
    o.name as org_name,
    o.level as org_level,
    COUNT(s.id) as total_students,
    COUNT(CASE WHEN s.gender = 'MALE' THEN 1 END) as male_count,
    COUNT(CASE WHEN s.gender = 'FEMALE' THEN 1 END) as female_count,
    COUNT(DISTINCT s.martial_art_id) as martial_arts_count,
    AVG(EXTRACT(YEAR FROM AGE(s.date_of_birth))) as avg_age
FROM organizations o
LEFT JOIN students s ON s.org_id = o.id AND s.is_active = true
GROUP BY o.id, o.name, o.level;

CREATE UNIQUE INDEX ON student_statistics (org_id);

-- Refresh materialized view daily
CREATE OR REPLACE FUNCTION refresh_student_statistics()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY student_statistics;
END;
$$ LANGUAGE plpgsql;

-- Schedule refresh (requires pg_cron extension)
SELECT cron.schedule('refresh-stats', '0 2 * * *', 'SELECT refresh_student_statistics();');
```

### 🚀 **API Performance**
```rust
// Connection pooling and caching
use sqlx::{PgPool, postgres::PgPoolOptions};
use redis::Client as RedisClient;
use std::time::Duration;

pub struct DatabaseManager {
    pg_pool: PgPool,
    redis_client: RedisClient,
}

impl DatabaseManager {
    pub async fn new(database_url: &str, redis_url: &str) -> Result<Self, Box<dyn std::error::Error>> {
        // PostgreSQL connection pool
        let pg_pool = PgPoolOptions::new()
            .max_connections(20)
            .min_connections(5)
            .acquire_timeout(Duration::from_secs(30))
            .idle_timeout(Duration::from_secs(600))
            .max_lifetime(Duration::from_secs(1800))
            .connect(database_url)
            .await?;

        // Redis client
        let redis_client = RedisClient::open(redis_url)?;

        Ok(Self {
            pg_pool,
            redis_client,
        })
    }
}

// Caching layer
use serde::{Deserialize, Serialize};
use redis::AsyncCommands;

pub struct CacheService {
    redis: redis::aio::Connection,
}

impl CacheService {
    pub async fn get<T>(&mut self, key: &str) -> Result<Option<T>, redis::RedisError>
    where
        T: for<'de> Deserialize<'de>,
    {
        let value: Option<String> = self.redis.get(key).await?;
        match value {
            Some(json) => Ok(serde_json::from_str(&json).ok()),
            None => Ok(None),
        }
    }

    pub async fn set<T>(&mut self, key: &str, value: &T, ttl: u64) -> Result<(), redis::RedisError>
    where
        T: Serialize,
    {
        let json = serde_json::to_string(value).map_err(|_| {
            redis::RedisError::from((redis::ErrorKind::TypeError, "Serialization failed"))
        })?;

        self.redis.set_ex(key, json, ttl).await
    }

    pub async fn delete(&mut self, key: &str) -> Result<(), redis::RedisError> {
        self.redis.del(key).await
    }
}

// API response caching middleware
use axum::{
    extract::{Request, State},
    middleware::Next,
    response::Response,
};

pub async fn cache_middleware(
    State(cache): State<Arc<Mutex<CacheService>>>,
    request: Request,
    next: Next,
) -> Response {
    let cache_key = format!("api:{}:{}", request.method(), request.uri().path());

    // Try to get from cache first
    if request.method() == "GET" {
        if let Ok(mut cache) = cache.try_lock() {
            if let Ok(Some(cached_response)) = cache.get::<String>(&cache_key).await {
                return Response::builder()
                    .header("Content-Type", "application/json")
                    .header("X-Cache", "HIT")
                    .body(cached_response.into())
                    .unwrap();
            }
        }
    }

    let response = next.run(request).await;

    // Cache successful GET responses
    if response.status().is_success() && request.method() == "GET" {
        if let Ok(mut cache) = cache.try_lock() {
            let body = response.body();
            // Cache for 5 minutes
            let _ = cache.set(&cache_key, &body, 300).await;
        }
    }

    response
}
```

---

## 🔒 **SECURITY IMPLEMENTATION**

### 🛡️ **Input Validation & Sanitization**
```rust
use validator::{Validate, ValidationError};
use serde::{Deserialize, Serialize};

#[derive(Debug, Deserialize, Validate)]
pub struct CreateStudentRequest {
    #[validate(length(min = 2, max = 100, message = "Tên phải từ 2-100 ký tự"))]
    pub full_name: String,

    #[validate(custom = "validate_date_of_birth")]
    pub date_of_birth: chrono::NaiveDate,

    #[validate(email(message = "Email không hợp lệ"))]
    pub email: Option<String>,

    #[validate(regex(path = "PHONE_REGEX", message = "Số điện thoại không hợp lệ"))]
    pub phone: Option<String>,

    #[validate(length(min = 9, max = 12, message = "CCCD phải từ 9-12 số"))]
    #[validate(regex(path = "CCCD_REGEX", message = "CCCD chỉ chứa số"))]
    pub cccd: Option<String>,

    pub organization_id: uuid::Uuid,
    pub martial_art_id: uuid::Uuid,
}

lazy_static::lazy_static! {
    static ref PHONE_REGEX: regex::Regex = regex::Regex::new(r"^[0-9+\-\s()]{10,15}$").unwrap();
    static ref CCCD_REGEX: regex::Regex = regex::Regex::new(r"^\d{9,12}$").unwrap();
}

fn validate_date_of_birth(date: &chrono::NaiveDate) -> Result<(), ValidationError> {
    let today = chrono::Utc::now().date_naive();
    let min_age = today - chrono::Duration::days(365 * 3); // 3 years old minimum
    let max_age = today - chrono::Duration::days(365 * 100); // 100 years old maximum

    if *date > min_age {
        return Err(ValidationError::new("Tuổi tối thiểu là 3 tuổi"));
    }

    if *date < max_age {
        return Err(ValidationError::new("Tuổi tối đa là 100 tuổi"));
    }

    Ok(())
}

// SQL injection prevention with parameterized queries
pub async fn search_students(
    pool: &PgPool,
    search_term: &str,
    org_id: uuid::Uuid,
    limit: i64,
    offset: i64,
) -> Result<Vec<Student>, sqlx::Error> {
    // Use parameterized queries to prevent SQL injection
    let students = sqlx::query_as!(
        Student,
        r#"
        SELECT id, student_code, full_name, date_of_birth, gender as "gender: Gender",
               address, phone, email, org_id, martial_art_id, current_rank_id,
               is_active, created_at, updated_at
        FROM students
        WHERE org_id = $1
          AND is_active = true
          AND (
            full_name ILIKE $2
            OR student_code ILIKE $2
            OR phone ILIKE $2
            OR email ILIKE $2
          )
        ORDER BY full_name
        LIMIT $3 OFFSET $4
        "#,
        org_id,
        format!("%{}%", search_term), // Safe parameterized search
        limit,
        offset
    )
    .fetch_all(pool)
    .await?;

    Ok(students)
}
```

### 🔐 **Rate Limiting & DDoS Protection**
```rust
use tower::ServiceBuilder;
use tower_http::limit::RequestBodyLimitLayer;
use tower_governor::{governor::GovernorConfigBuilder, GovernorLayer};
use std::time::Duration;

// Rate limiting configuration
pub fn create_rate_limiter() -> GovernorLayer<'static, (), axum::extract::ConnectInfo<std::net::SocketAddr>> {
    let governor_conf = Box::new(
        GovernorConfigBuilder::default()
            .per_second(10) // 10 requests per second
            .burst_size(20) // Allow bursts up to 20 requests
            .finish()
            .unwrap(),
    );

    GovernorLayer {
        config: Box::leak(governor_conf),
    }
}

// Security middleware stack
pub fn security_middleware() -> ServiceBuilder<
    tower::layer::util::Stack<
        tower_http::limit::RequestBodyLimitLayer,
        tower::layer::util::Stack<
            tower_http::cors::CorsLayer,
            tower_http::trace::TraceLayer,
        >,
    >,
> {
    ServiceBuilder::new()
        .layer(tower_http::trace::TraceLayer::new_for_http())
        .layer(
            tower_http::cors::CorsLayer::new()
                .allow_origin(tower_http::cors::Any)
                .allow_methods([
                    axum::http::Method::GET,
                    axum::http::Method::POST,
                    axum::http::Method::PUT,
                    axum::http::Method::DELETE,
                ])
                .allow_headers(tower_http::cors::Any),
        )
        .layer(RequestBodyLimitLayer::new(1024 * 1024 * 10)) // 10MB limit
}

// IP-based rate limiting with Redis
use redis::AsyncCommands;

pub struct IpRateLimiter {
    redis: redis::aio::Connection,
    max_requests: u32,
    window_seconds: u32,
}

impl IpRateLimiter {
    pub async fn check_rate_limit(&mut self, ip: &str) -> Result<bool, redis::RedisError> {
        let key = format!("rate_limit:{}", ip);
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let window_start = current_time - (current_time % self.window_seconds as u64);
        let window_key = format!("{}:{}", key, window_start);

        let current_count: u32 = self.redis.get(&window_key).await.unwrap_or(0);

        if current_count >= self.max_requests {
            return Ok(false); // Rate limit exceeded
        }

        // Increment counter
        let _: () = self.redis.incr(&window_key, 1).await?;
        let _: () = self.redis.expire(&window_key, self.window_seconds as usize).await?;

        Ok(true) // Request allowed
    }
}
```

---

**📄 Tài liệu này bao gồm:**
- 🏗️ System Architecture chi tiết
- 🔐 Authentication & Authorization patterns
- 📊 Database design patterns & optimization
- 🔄 State machine implementation
- 📱 Mobile app architecture (React Native)
- 🌐 Next.js website architecture
- ⚡ Performance optimization strategies
- 🔒 Security implementation guidelines

**🎯 Mục đích sử dụng:**
- Reference cho development team
- Code review guidelines
- Architecture decision documentation
- Implementation best practices
- Security compliance checklist