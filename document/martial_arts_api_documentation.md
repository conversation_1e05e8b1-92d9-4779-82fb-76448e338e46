# 🔌 HỆ THỐNG QUẢN LÝ HỌC VIÊN VÕ THUẬT - API DOCUMENTATION
## REST API Specification & Integration Guide

---

## 🌐 **API BASE INFORMATION**

### 📋 **General Information**
- **Base URL**: `https://api.martial-arts-system.com/v1`
- **Protocol**: HTTPS only
- **Authentication**: JWT Bearer Token
- **Content Type**: `application/json`
- **Rate Limiting**: 100 requests/minute per IP
- **API Version**: v1.0.0

### 🔐 **Authentication Headers**
```http
Authorization: Bearer <jwt_access_token>
Content-Type: application/json
X-Organization-ID: <organization_uuid>
X-Request-ID: <unique_request_id>
```

---

## 🔑 **AUTHENTICATION ENDPOINTS**

### 🚪 **POST /auth/login**
Authenticate user and get access tokens.

**Request Body:**
```json
{
  "username": "<EMAIL>",
  "password": "SecurePassword123!",
  "remember_me": true
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "username": "<EMAIL>",
      "full_name": "Nguyễn Văn Admin",
      "role": "PROVINCE_ADMIN",
      "organization": {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "Liên đoàn Võ thuật TP.HCM",
        "level": "PROVINCE"
      }
    },
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600,
    "token_type": "Bearer"
  }
}
```

### 🔄 **POST /auth/refresh**
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 🚪 **POST /auth/logout**
Logout and invalidate tokens.

**Request Body:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

---

## 🏢 **ORGANIZATION ENDPOINTS**

### 📋 **GET /organizations**
Get organization list with hierarchy.

**Query Parameters:**
- `level`: Filter by organization level (NATIONAL, PROVINCE, DISTRICT, DEPARTMENT)
- `parent_id`: Filter by parent organization
- `status`: Filter by status (ACTIVE, PENDING, SUSPENDED)
- `search`: Search by name or code
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "organizations": [
      {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "Liên đoàn Võ thuật TP.HCM",
        "code": "LDVT-HCM",
        "level": "PROVINCE",
        "parent_id": "550e8400-e29b-41d4-a716-************",
        "address": "123 Nguyễn Huệ, Q1, TP.HCM",
        "phone": "028-1234-5678",
        "email": "<EMAIL>",
        "manager_name": "Nguyễn Văn Quản Lý",
        "status": "ACTIVE",
        "student_count": 1250,
        "children": [
          {
            "id": "550e8400-e29b-41d4-a716-446655440002",
            "name": "Võ thuật Quận 1",
            "level": "DISTRICT",
            "student_count": 300
          }
        ],
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "total_pages": 3
    }
  }
}
```

### 🏢 **POST /organizations**
Create new organization.

**Request Body:**
```json
{
  "name": "Võ thuật Quận Tân Bình",
  "code": "VT-TB",
  "level": "DISTRICT",
  "parent_id": "550e8400-e29b-41d4-a716-************",
  "address": "456 Cộng Hòa, Tân Bình, TP.HCM",
  "phone": "028-9876-5432",
  "email": "<EMAIL>",
  "manager_name": "Trần Thị Quản Lý",
  "manager_phone": "0901-234-567",
  "manager_email": "<EMAIL>"
}
```

---

## 👥 **STUDENT ENDPOINTS**

### 📋 **GET /students**
Get student list with filtering and search.

**Query Parameters:**
- `organization_id`: Filter by organization
- `martial_art_id`: Filter by martial art
- `rank_id`: Filter by current rank
- `instructor_id`: Filter by instructor
- `gender`: Filter by gender (MALE, FEMALE, OTHER)
- `age_min`: Minimum age
- `age_max`: Maximum age
- `search`: Search by name, code, phone, email
- `is_active`: Filter by active status (default: true)
- `enrolled_from`: Enrolled date from (YYYY-MM-DD)
- `enrolled_to`: Enrolled date to (YYYY-MM-DD)
- `sort`: Sort field (name, code, enrolled_at)
- `order`: Sort order (asc, desc)
- `page`: Page number
- `limit`: Items per page

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "students": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440010",
        "student_code": "HV001234",
        "full_name": "Nguyễn Văn Học Viên",
        "date_of_birth": "2010-05-15",
        "gender": "MALE",
        "age": 14,
        "address": "789 Lê Lợi, Q1, TP.HCM",
        "phone": "0912-345-678",
        "email": "<EMAIL>",
        "organization": {
          "id": "550e8400-e29b-41d4-a716-446655440002",
          "name": "Võ thuật Quận 1",
          "level": "DISTRICT"
        },
        "martial_art": {
          "id": "550e8400-e29b-41d4-a716-446655440020",
          "name": "Karate Shotokan",
          "type": "KARATE"
        },
        "current_rank": {
          "id": "550e8400-e29b-41d4-a716-446655440030",
          "name": "Yellow Belt",
          "level": "YELLOW",
          "color": "Yellow"
        },
        "instructor": {
          "id": "550e8400-e29b-41d4-a716-446655440040",
          "full_name": "Thầy Nguyễn Văn Huấn Luyện"
        },
        "enrolled_at": "2023-09-01T00:00:00Z",
        "is_active": true,
        "created_at": "2023-09-01T10:30:00Z",
        "updated_at": "2024-01-15T14:20:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1250,
      "total_pages": 63
    },
    "statistics": {
      "total_students": 1250,
      "male_count": 750,
      "female_count": 500,
      "avg_age": 16.5,
      "new_this_month": 45
    }
  }
}
```

### 👤 **GET /students/{id}**
Get detailed student information.

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "student": {
      "id": "550e8400-e29b-41d4-a716-446655440010",
      "student_code": "HV001234",
      "full_name": "Nguyễn Văn Học Viên",
      "date_of_birth": "2010-05-15",
      "gender": "MALE",
      "address": "789 Lê Lợi, Q1, TP.HCM",
      "phone": "0912-345-678",
      "email": "<EMAIL>",
      "cccd": "123456789",
      "emergency_contact": {
        "name": "Nguyễn Thị Mẹ",
        "phone": "0987-654-321",
        "relationship": "Mẹ"
      },
      "medical_conditions": "Không có",
      "allergies": "Không có",
      "photo_url": "https://cdn.example.com/photos/student_123.jpg",
      "organization": {
        "id": "550e8400-e29b-41d4-a716-446655440002",
        "name": "Võ thuật Quận 1",
        "level": "DISTRICT",
        "address": "123 Nguyễn Huệ, Q1, TP.HCM"
      },
      "martial_art": {
        "id": "550e8400-e29b-41d4-a716-446655440020",
        "name": "Karate Shotokan",
        "type": "KARATE",
        "description": "Traditional Japanese martial art"
      },
      "current_rank": {
        "id": "550e8400-e29b-41d4-a716-446655440030",
        "name": "Yellow Belt",
        "level": "YELLOW",
        "color": "Yellow",
        "order_index": 2
      },
      "instructor": {
        "id": "550e8400-e29b-41d4-a716-446655440040",
        "full_name": "Thầy Nguyễn Văn Huấn Luyện",
        "phone": "0901-111-222",
        "email": "<EMAIL>"
      },
      "rank_history": [
        {
          "rank_name": "White Belt",
          "achieved_date": "2023-09-01",
          "exam_id": null
        },
        {
          "rank_name": "Yellow Belt",
          "achieved_date": "2024-01-15",
          "exam_id": "550e8400-e29b-41d4-a716-446655440050"
        }
      ],
      "certificates": [
        {
          "id": "550e8400-e29b-41d4-a716-446655440060",
          "certificate_number": "CC-2024-001234",
          "rank_name": "Yellow Belt",
          "issued_date": "2024-01-20",
          "status": "ISSUED",
          "pdf_url": "https://cdn.example.com/certificates/CC-2024-001234.pdf"
        }
      ],
      "enrolled_at": "2023-09-01T00:00:00Z",
      "is_active": true,
      "created_at": "2023-09-01T10:30:00Z",
      "updated_at": "2024-01-15T14:20:00Z"
    }
  }
}
```

### 👤 **POST /students**
Create new student.

**Request Body:**
```json
{
  "full_name": "Trần Văn Học Viên Mới",
  "date_of_birth": "2012-03-20",
  "gender": "MALE",
  "address": "456 Lê Thánh Tôn, Q1, TP.HCM",
  "phone": "0913-456-789",
  "email": "<EMAIL>",
  "cccd": "987654321",
  "emergency_contact_name": "Trần Thị Mẹ",
  "emergency_contact_phone": "0988-777-666",
  "emergency_contact_relationship": "Mẹ",
  "organization_id": "550e8400-e29b-41d4-a716-446655440002",
  "martial_art_id": "550e8400-e29b-41d4-a716-446655440020",
  "instructor_id": "550e8400-e29b-41d4-a716-446655440040",
  "medical_conditions": "Không có",
  "allergies": "Dị ứng phấn hoa",
  "notes": "Học viên mới, cần theo dõi kỹ"
}
```

### 👤 **PUT /students/{id}**
Update student information.

### 🗑️ **DELETE /students/{id}**
Soft delete student (set is_active = false).

---

## 📝 **EXAM ENDPOINTS**

### 📋 **GET /exams**
Get exam list with filtering.

**Query Parameters:**
- `organization_id`: Filter by organization
- `martial_art_id`: Filter by martial art
- `status`: Filter by status
- `exam_date_from`: Exam date from
- `exam_date_to`: Exam date to
- `from_rank_id`: Filter by source rank
- `to_rank_id`: Filter by target rank

### 📝 **POST /exams**
Create new exam.

**Request Body:**
```json
{
  "title": "Kỳ thi thăng đai Karate tháng 3/2024",
  "description": "Kỳ thi thăng đai từ Đai Vàng lên Đai Cam",
  "martial_art_id": "550e8400-e29b-41d4-a716-446655440020",
  "from_rank_id": "550e8400-e29b-41d4-a716-446655440030",
  "to_rank_id": "550e8400-e29b-41d4-a716-446655440031",
  "organization_id": "550e8400-e29b-41d4-a716-446655440002",
  "exam_date": "2024-03-15",
  "exam_time": "09:00:00",
  "location": "Phòng thi Võ thuật Quận 1",
  "max_participants": 50,
  "registration_deadline": "2024-03-10",
  "fee": 200000,
  "requirements": "Đã học tối thiểu 6 tháng ở đai hiện tại",
  "syllabus": "Kata cơ bản, đối kháng, lý thuyết",
  "examiner_ids": [
    "550e8400-e29b-41d4-a716-446655440040",
    "550e8400-e29b-41d4-a716-446655440041"
  ]
}
```

### 📝 **POST /exams/{id}/register**
Register student for exam.

**Request Body:**
```json
{
  "student_id": "550e8400-e29b-41d4-a716-446655440010",
  "payment_amount": 200000,
  "payment_reference": "TXN123456789",
  "notes": "Đã thanh toán đầy đủ"
}
```

### 📊 **POST /exams/{id}/results**
Submit exam results.

**Request Body:**
```json
{
  "results": [
    {
      "student_id": "550e8400-e29b-41d4-a716-446655440010",
      "result": "PASS",
      "score": 85.5,
      "max_score": 100,
      "practical_score": 45,
      "theory_score": 40.5,
      "examiner_notes": "Kỹ thuật tốt, cần cải thiện tốc độ"
    }
  ]
}
```

---

## 🏆 **CERTIFICATE ENDPOINTS**

### 📜 **GET /certificates**
Get certificate list.

### 📜 **POST /certificates**
Generate new certificate.

**Request Body:**
```json
{
  "student_id": "550e8400-e29b-41d4-a716-446655440010",
  "exam_id": "550e8400-e29b-41d4-a716-446655440050",
  "rank_id": "550e8400-e29b-41d4-a716-446655440031",
  "issued_date": "2024-03-20",
  "valid_from": "2024-03-20",
  "valid_until": "2026-03-20",
  "template_id": "550e8400-e29b-41d4-a716-446655440070"
}
```

### 📜 **GET /certificates/{id}/download**
Download certificate PDF.

### 🔍 **GET /certificates/verify/{certificate_number}**
Verify certificate authenticity.

---

## 🔄 **TRANSFER REQUEST ENDPOINTS**

### 📋 **GET /transfer-requests**
Get transfer request list.

### 🔄 **POST /transfer-requests**
Create transfer request.

**Request Body:**
```json
{
  "student_id": "550e8400-e29b-41d4-a716-446655440010",
  "from_org_id": "550e8400-e29b-41d4-a716-446655440002",
  "to_org_id": "550e8400-e29b-41d4-a716-446655440003",
  "effective_date": "2024-04-01",
  "reason": "Gia đình chuyển nhà đến Quận 3"
}
```

### ✅ **POST /transfer-requests/{id}/approve-from**
Approve transfer from source organization.

### ✅ **POST /transfer-requests/{id}/approve-to**
Approve transfer from destination organization.

### ❌ **POST /transfer-requests/{id}/reject**
Reject transfer request.

---

## 📊 **ATTENDANCE ENDPOINTS**

### 📅 **GET /attendance/sessions**
Get attendance sessions.

### 📅 **POST /attendance/sessions**
Create attendance session.

### ✅ **POST /attendance/sessions/{id}/mark**
Mark student attendance.

**Request Body:**
```json
{
  "student_id": "550e8400-e29b-41d4-a716-446655440010",
  "status": "PRESENT",
  "check_in_time": "2024-03-15T09:15:00Z",
  "location_lat": 10.7769,
  "location_lng": 106.7009,
  "method": "QR_CODE",
  "device_info": {
    "platform": "iOS",
    "version": "17.0",
    "model": "iPhone 14"
  }
}
```

---

## 📊 **REPORTING ENDPOINTS**

### 📈 **GET /reports/dashboard**
Get dashboard statistics.

### 📊 **GET /reports/students**
Get student reports with various filters.

### 📈 **GET /reports/exams**
Get exam statistics and reports.

### 📊 **POST /reports/export**
Export reports to various formats (PDF, Excel, CSV).

---

## ⚠️ **ERROR RESPONSES**

### 🔴 **Standard Error Format**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Dữ liệu đầu vào không hợp lệ",
    "details": [
      {
        "field": "email",
        "message": "Email không đúng định dạng"
      },
      {
        "field": "phone",
        "message": "Số điện thoại phải có 10-11 số"
      }
    ],
    "request_id": "req_123456789",
    "timestamp": "2024-03-15T10:30:00Z"
  }
}
```

### 📋 **Common Error Codes**
- `VALIDATION_ERROR` (400): Input validation failed
- `UNAUTHORIZED` (401): Authentication required
- `FORBIDDEN` (403): Insufficient permissions
- `NOT_FOUND` (404): Resource not found
- `CONFLICT` (409): Resource conflict
- `RATE_LIMIT_EXCEEDED` (429): Too many requests
- `INTERNAL_ERROR` (500): Server error

---

**📚 API Documentation Version:** 1.0.0  
**📅 Last Updated:** 2024-03-15  
**🔗 Postman Collection:** Available upon request  
**🧪 Testing Environment:** `https://api-staging.martial-arts-system.com/v1`
