-- =====================================================
-- HỆ THỐNG QUẢN LÝ HỌC VIÊN VÕ THUẬT 4 CẤP ĐỘ
-- DATABASE SCHEMA HOÀN CHỈNH - PostgreSQL
-- =====================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- ENUM TYPES
-- =====================================================

CREATE TYPE organization_level AS ENUM ('NATIONAL', 'PROVINCE', 'DISTRICT', 'DEPARTMENT');
CREATE TYPE organization_status AS ENUM ('PENDING', 'ACTIVE', 'SUSPENDED', 'INACTIVE');
CREATE TYPE user_role AS ENUM ('SUPER_ADMIN', 'NATIONAL_ADMIN', 'PROVINCE_ADMIN', 'DISTRICT_ADMIN', 'DEPARTMENT_ADMIN', 'INSTRUCTOR', 'STUDENT');
CREATE TYPE gender_type AS ENUM ('MALE', 'FEMALE', 'OTHER');
CREATE TYPE martial_art_type AS ENUM ('KARATE', 'TAEKWONDO', 'JUDO', 'AIKIDO', 'KUNGFU', 'VOVINAM', 'OTHER');
CREATE TYPE rank_level AS ENUM ('WHITE', 'YELLOW', 'ORANGE', 'GREEN', 'BLUE', 'BROWN', 'BLACK_1', 'BLACK_2', 'BLACK_3', 'BLACK_4', 'BLACK_5', 'MASTER');
CREATE TYPE exam_status AS ENUM ('SCHEDULED', 'REGISTRATION_OPEN', 'REGISTRATION_CLOSED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED');
CREATE TYPE exam_result AS ENUM ('PASS', 'FAIL', 'ABSENT', 'PENDING');
CREATE TYPE transfer_status AS ENUM ('PENDING', 'APPROVED_BY_FROM', 'APPROVED_BY_TO', 'APPROVED_BY_PARENT', 'COMPLETED', 'REJECTED');
CREATE TYPE certificate_status AS ENUM ('DRAFT', 'ISSUED', 'REVOKED', 'EXPIRED');
CREATE TYPE attendance_status AS ENUM ('PRESENT', 'ABSENT', 'LATE', 'EXCUSED');
CREATE TYPE notification_type AS ENUM ('INFO', 'WARNING', 'SUCCESS', 'ERROR');
CREATE TYPE material_type AS ENUM ('DOCUMENT', 'VIDEO', 'IMAGE', 'AUDIO', 'OTHER');

-- =====================================================
-- CORE TABLES
-- =====================================================

-- Organizations (4-level hierarchy)
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    level organization_level NOT NULL,
    parent_id UUID REFERENCES organizations(id) ON DELETE SET NULL,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    website VARCHAR(200),
    manager_name VARCHAR(100),
    manager_phone VARCHAR(20),
    manager_email VARCHAR(100),
    established_date DATE,
    registration_number VARCHAR(100),
    tax_code VARCHAR(20),
    status organization_status DEFAULT 'PENDING',
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID,
    updated_by UUID,
    
    CONSTRAINT valid_hierarchy CHECK (
        (level = 'NATIONAL' AND parent_id IS NULL) OR
        (level = 'PROVINCE' AND parent_id IS NOT NULL) OR
        (level = 'DISTRICT' AND parent_id IS NOT NULL) OR
        (level = 'DEPARTMENT' AND parent_id IS NOT NULL)
    )
);

-- Users (system accounts)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    avatar_url VARCHAR(500),
    role user_role NOT NULL,
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMPTZ,
    password_changed_at TIMESTAMPTZ DEFAULT NOW(),
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMPTZ,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- Martial Arts Types
CREATE TABLE martial_arts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    type martial_art_type NOT NULL,
    description TEXT,
    origin_country VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Ranks/Belts
CREATE TABLE ranks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    martial_art_id UUID NOT NULL REFERENCES martial_arts(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    level rank_level NOT NULL,
    color VARCHAR(50),
    order_index INTEGER NOT NULL,
    min_age INTEGER,
    min_training_months INTEGER,
    requirements TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(martial_art_id, level),
    UNIQUE(martial_art_id, order_index)
);

-- Students
CREATE TABLE students (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_code VARCHAR(50) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    gender gender_type NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    cccd VARCHAR(20) UNIQUE,
    passport VARCHAR(20),
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relationship VARCHAR(50),
    photo_url VARCHAR(500),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    martial_art_id UUID NOT NULL REFERENCES martial_arts(id) ON DELETE RESTRICT,
    current_rank_id UUID REFERENCES ranks(id) ON DELETE SET NULL,
    instructor_id UUID REFERENCES users(id) ON DELETE SET NULL,
    enrolled_at TIMESTAMPTZ DEFAULT NOW(),
    medical_conditions TEXT,
    allergies TEXT,
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    
    CONSTRAINT valid_age CHECK (date_of_birth <= CURRENT_DATE - INTERVAL '3 years'),
    CONSTRAINT valid_phone CHECK (phone ~ '^[0-9+\-\s()]+$'),
    CONSTRAINT valid_email CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Exams
CREATE TABLE exams (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    martial_art_id UUID NOT NULL REFERENCES martial_arts(id) ON DELETE CASCADE,
    from_rank_id UUID NOT NULL REFERENCES ranks(id) ON DELETE CASCADE,
    to_rank_id UUID NOT NULL REFERENCES ranks(id) ON DELETE CASCADE,
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    exam_date DATE NOT NULL,
    exam_time TIME NOT NULL,
    location VARCHAR(200),
    max_participants INTEGER,
    registration_deadline DATE NOT NULL,
    fee DECIMAL(10,2) DEFAULT 0,
    status exam_status DEFAULT 'SCHEDULED',
    requirements TEXT,
    syllabus TEXT,
    examiner_ids UUID[] DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    
    CONSTRAINT valid_exam_date CHECK (exam_date >= CURRENT_DATE),
    CONSTRAINT valid_registration_deadline CHECK (registration_deadline <= exam_date),
    CONSTRAINT valid_rank_progression CHECK (from_rank_id != to_rank_id)
);

-- Exam Registrations
CREATE TABLE exam_registrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    exam_id UUID NOT NULL REFERENCES exams(id) ON DELETE CASCADE,
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    registered_at TIMESTAMPTZ DEFAULT NOW(),
    registered_by UUID NOT NULL REFERENCES users(id),
    payment_status VARCHAR(20) DEFAULT 'PENDING',
    payment_date TIMESTAMPTZ,
    payment_amount DECIMAL(10,2),
    payment_reference VARCHAR(100),
    notes TEXT,
    
    UNIQUE(exam_id, student_id)
);

-- Exam Results
CREATE TABLE exam_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    exam_id UUID NOT NULL REFERENCES exams(id) ON DELETE CASCADE,
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    result exam_result NOT NULL,
    score DECIMAL(5,2),
    max_score DECIMAL(5,2),
    practical_score DECIMAL(5,2),
    theory_score DECIMAL(5,2),
    examiner_notes TEXT,
    examined_at TIMESTAMPTZ DEFAULT NOW(),
    examined_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(exam_id, student_id),
    CONSTRAINT valid_scores CHECK (
        score >= 0 AND score <= max_score AND
        practical_score >= 0 AND theory_score >= 0
    )
);

-- Certificates
CREATE TABLE certificates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    certificate_number VARCHAR(100) UNIQUE NOT NULL,
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    exam_id UUID REFERENCES exams(id) ON DELETE SET NULL,
    martial_art_id UUID NOT NULL REFERENCES martial_arts(id) ON DELETE RESTRICT,
    rank_id UUID NOT NULL REFERENCES ranks(id) ON DELETE RESTRICT,
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    issued_date DATE NOT NULL DEFAULT CURRENT_DATE,
    valid_from DATE NOT NULL DEFAULT CURRENT_DATE,
    valid_until DATE,
    status certificate_status DEFAULT 'DRAFT',
    template_id UUID,
    pdf_url VARCHAR(500),
    qr_code VARCHAR(500),
    digital_signature TEXT,
    signature_hash VARCHAR(255),
    issued_by UUID NOT NULL REFERENCES users(id),
    verified_by UUID REFERENCES users(id),
    revoked_at TIMESTAMPTZ,
    revoked_by UUID REFERENCES users(id),
    revocation_reason TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT valid_validity_period CHECK (valid_until IS NULL OR valid_until > valid_from)
);

-- Transfer Requests (2-level approval workflow)
CREATE TABLE transfer_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transfer_code VARCHAR(50) UNIQUE NOT NULL,
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    from_org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    to_org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    request_date TIMESTAMPTZ DEFAULT NOW(),
    effective_date DATE,
    reason TEXT NOT NULL,
    status transfer_status DEFAULT 'PENDING',
    
    -- Approval tracking
    approved_by_from UUID REFERENCES users(id),
    approved_by_from_at TIMESTAMPTZ,
    approved_by_from_notes TEXT,
    
    approved_by_to UUID REFERENCES users(id),
    approved_by_to_at TIMESTAMPTZ,
    approved_by_to_notes TEXT,
    
    approved_by_parent UUID REFERENCES users(id),
    approved_by_parent_at TIMESTAMPTZ,
    approved_by_parent_notes TEXT,
    
    completed_at TIMESTAMPTZ,
    completed_by UUID REFERENCES users(id),
    
    rejected_at TIMESTAMPTZ,
    rejected_by UUID REFERENCES users(id),
    rejection_reason TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    
    CONSTRAINT different_organizations CHECK (from_org_id != to_org_id)
);

-- Attendance Sessions
CREATE TABLE attendance_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    martial_art_id UUID REFERENCES martial_arts(id) ON DELETE SET NULL,
    instructor_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    location VARCHAR(200),
    max_participants INTEGER,
    qr_code VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES users(id),
    
    CONSTRAINT valid_session_time CHECK (end_time > start_time)
);

-- Attendance Records
CREATE TABLE attendance_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES attendance_sessions(id) ON DELETE CASCADE,
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    status attendance_status NOT NULL,
    check_in_time TIMESTAMPTZ,
    check_out_time TIMESTAMPTZ,
    location_lat DECIMAL(10, 8),
    location_lng DECIMAL(11, 8),
    device_info JSONB,
    notes TEXT,
    marked_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(session_id, student_id),
    CONSTRAINT valid_check_times CHECK (check_out_time IS NULL OR check_out_time > check_in_time)
);

-- Learning Materials
CREATE TABLE learning_materials (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    type material_type NOT NULL,
    file_url VARCHAR(500),
    file_size BIGINT,
    file_mime_type VARCHAR(100),
    thumbnail_url VARCHAR(500),
    martial_art_id UUID REFERENCES martial_arts(id) ON DELETE SET NULL,
    rank_id UUID REFERENCES ranks(id) ON DELETE SET NULL,
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    is_public BOOLEAN DEFAULT FALSE,
    download_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    tags TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- Student History (audit trail)
CREATE TABLE student_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    old_values JSONB,
    new_values JSONB,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES users(id)
);

-- Notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type notification_type DEFAULT 'INFO',
    recipient_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES users(id) ON DELETE SET NULL,
    related_entity_type VARCHAR(50),
    related_entity_id UUID,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMPTZ,
    sent_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Organizations
CREATE INDEX idx_organizations_parent_id ON organizations(parent_id);
CREATE INDEX idx_organizations_level ON organizations(level);
CREATE INDEX idx_organizations_status ON organizations(status);
CREATE INDEX idx_organizations_code ON organizations(code);

-- Users
CREATE INDEX idx_users_org_id ON users(org_id);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_is_active ON users(is_active);

-- Students
CREATE INDEX idx_students_org_id ON students(org_id);
CREATE INDEX idx_students_martial_art_id ON students(martial_art_id);
CREATE INDEX idx_students_current_rank_id ON students(current_rank_id);
CREATE INDEX idx_students_instructor_id ON students(instructor_id);
CREATE INDEX idx_students_is_active ON students(is_active);
CREATE INDEX idx_students_full_name ON students USING gin(to_tsvector('english', full_name));
CREATE INDEX idx_students_student_code ON students(student_code);

-- Exams
CREATE INDEX idx_exams_org_id ON exams(org_id);
CREATE INDEX idx_exams_martial_art_id ON exams(martial_art_id);
CREATE INDEX idx_exams_exam_date ON exams(exam_date);
CREATE INDEX idx_exams_status ON exams(status);
CREATE INDEX idx_exams_from_rank_id ON exams(from_rank_id);
CREATE INDEX idx_exams_to_rank_id ON exams(to_rank_id);

-- Exam Registrations
CREATE INDEX idx_exam_registrations_exam_id ON exam_registrations(exam_id);
CREATE INDEX idx_exam_registrations_student_id ON exam_registrations(student_id);
CREATE INDEX idx_exam_registrations_payment_status ON exam_registrations(payment_status);

-- Certificates
CREATE INDEX idx_certificates_student_id ON certificates(student_id);
CREATE INDEX idx_certificates_org_id ON certificates(org_id);
CREATE INDEX idx_certificates_status ON certificates(status);
CREATE INDEX idx_certificates_certificate_number ON certificates(certificate_number);
CREATE INDEX idx_certificates_issued_date ON certificates(issued_date);

-- Transfer Requests
CREATE INDEX idx_transfer_requests_student_id ON transfer_requests(student_id);
CREATE INDEX idx_transfer_requests_from_org_id ON transfer_requests(from_org_id);
CREATE INDEX idx_transfer_requests_to_org_id ON transfer_requests(to_org_id);
CREATE INDEX idx_transfer_requests_status ON transfer_requests(status);
CREATE INDEX idx_transfer_requests_request_date ON transfer_requests(request_date);

-- Attendance
CREATE INDEX idx_attendance_sessions_org_id ON attendance_sessions(org_id);
CREATE INDEX idx_attendance_sessions_session_date ON attendance_sessions(session_date);
CREATE INDEX idx_attendance_records_session_id ON attendance_records(session_id);
CREATE INDEX idx_attendance_records_student_id ON attendance_records(student_id);

-- Notifications
CREATE INDEX idx_notifications_recipient_id ON notifications(recipient_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_notifications_sent_at ON notifications(sent_at);

-- Student History
CREATE INDEX idx_student_history_student_id ON student_history(student_id);
CREATE INDEX idx_student_history_created_at ON student_history(created_at);
CREATE INDEX idx_student_history_action ON student_history(action);

-- =====================================================
-- TRIGGERS FOR AUDIT TRAIL
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at trigger to all relevant tables
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_students_updated_at BEFORE UPDATE ON students FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_exams_updated_at BEFORE UPDATE ON exams FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_certificates_updated_at BEFORE UPDATE ON certificates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_transfer_requests_updated_at BEFORE UPDATE ON transfer_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================

-- Insert sample martial arts
INSERT INTO martial_arts (name, type, description, origin_country) VALUES
('Karate Shotokan', 'KARATE', 'Traditional Japanese martial art focusing on linear techniques', 'Japan'),
('Taekwondo WTF', 'TAEKWONDO', 'Korean martial art emphasizing high kicks and fast techniques', 'South Korea'),
('Judo', 'JUDO', 'Japanese martial art focusing on throws and grappling', 'Japan'),
('Vovinam', 'VOVINAM', 'Vietnamese martial art combining hard and soft techniques', 'Vietnam');

-- Insert sample ranks for Karate
INSERT INTO ranks (martial_art_id, name, level, color, order_index, min_age, min_training_months) 
SELECT id, 'White Belt', 'WHITE', 'White', 1, 5, 0 FROM martial_arts WHERE name = 'Karate Shotokan';

INSERT INTO ranks (martial_art_id, name, level, color, order_index, min_age, min_training_months) 
SELECT id, 'Yellow Belt', 'YELLOW', 'Yellow', 2, 6, 6 FROM martial_arts WHERE name = 'Karate Shotokan';

INSERT INTO ranks (martial_art_id, name, level, color, order_index, min_age, min_training_months) 
SELECT id, 'Black Belt 1st Dan', 'BLACK_1', 'Black', 8, 16, 36 FROM martial_arts WHERE name = 'Karate Shotokan';

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- Student details with organization and rank info
CREATE VIEW student_details AS
SELECT 
    s.id,
    s.student_code,
    s.full_name,
    s.date_of_birth,
    s.gender,
    s.phone,
    s.email,
    s.enrolled_at,
    s.is_active,
    o.name as organization_name,
    o.level as organization_level,
    ma.name as martial_art_name,
    r.name as current_rank_name,
    r.color as current_rank_color,
    u.full_name as instructor_name
FROM students s
LEFT JOIN organizations o ON s.org_id = o.id
LEFT JOIN martial_arts ma ON s.martial_art_id = ma.id
LEFT JOIN ranks r ON s.current_rank_id = r.id
LEFT JOIN users u ON s.instructor_id = u.id;

-- Organization hierarchy view
CREATE VIEW organization_hierarchy AS
WITH RECURSIVE org_tree AS (
    SELECT id, name, code, level, parent_id, 0 as depth, ARRAY[name] as path
    FROM organizations 
    WHERE parent_id IS NULL
    
    UNION ALL
    
    SELECT o.id, o.name, o.code, o.level, o.parent_id, ot.depth + 1, ot.path || o.name
    FROM organizations o
    JOIN org_tree ot ON o.parent_id = ot.id
)
SELECT * FROM org_tree;

-- =====================================================
-- SECURITY POLICIES (Row Level Security)
-- =====================================================

-- Enable RLS on sensitive tables
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE transfer_requests ENABLE ROW LEVEL SECURITY;

-- Example policy: Users can only see students from their organization or sub-organizations
CREATE POLICY student_access_policy ON students
    FOR ALL TO authenticated_users
    USING (
        org_id IN (
            SELECT id FROM organization_hierarchy 
            WHERE path @> (SELECT ARRAY[o.name] FROM organizations o 
                          JOIN users u ON o.id = u.org_id 
                          WHERE u.id = current_user_id())
        )
    );

-- =====================================================
-- END OF SCHEMA
-- =====================================================
