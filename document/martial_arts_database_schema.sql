-- =====================================================
-- HỆ THỐNG QUẢN LÝ HỌC VIÊN VÕ THUẬT 4 CẤP ĐỘ
-- Database Schema Design
-- =====================================================

-- Extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- 1. BẢNG QUẢN LÝ TỔ CHỨC 4 CẤP ĐỘ
-- =====================================================

-- Cấp độ tổ chức
CREATE TYPE organization_level AS ENUM ('NATIONAL', 'CITY', 'WARD', 'DEPARTMENT');
CREATE TYPE organization_status AS ENUM ('PENDING', 'ACTIVE', 'SUSPENDED', 'INACTIVE');

-- Bảng tổ chức chính
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(20) UNIQUE NOT NULL, -- Mã đơn vị: VN001, HN001, BD001, KR001
    name VARCHAR(255) NOT NULL,
    level organization_level NOT NULL,
    parent_id UUID REFERENCES organizations(id),
    status organization_status DEFAULT 'PENDING',
    
    -- Thông tin liên hệ
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    website VARCHAR(255),
    
    -- Thông tin người đại diện
    representative_name VARCHAR(100),
    representative_phone VARCHAR(20),
    representative_email VARCHAR(100),
    representative_id_card VARCHAR(20),
    
    -- Thông tin hoạt động
    established_date DATE,
    license_number VARCHAR(50),
    description TEXT,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID
);

-- Bảng môn võ
CREATE TYPE martial_art_status AS ENUM ('ACTIVE', 'INACTIVE');

CREATE TABLE martial_arts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(10) UNIQUE NOT NULL, -- KR, TKD, JD, VV
    name VARCHAR(100) NOT NULL,
    description TEXT,
    origin_country VARCHAR(50),
    status martial_art_status DEFAULT 'ACTIVE',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng đẳng cấp/đai
CREATE TABLE ranks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    martial_art_id UUID NOT NULL REFERENCES martial_arts(id),
    code VARCHAR(10) NOT NULL, -- WHITE, YELLOW, ORANGE, GREEN, BLUE, BROWN, BLACK_1, BLACK_2...
    name VARCHAR(50) NOT NULL,
    level INTEGER NOT NULL, -- 1, 2, 3, 4, 5...
    color VARCHAR(20),
    description TEXT,
    min_training_months INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(martial_art_id, code),
    UNIQUE(martial_art_id, level)
);

-- =====================================================
-- 2. BẢNG QUẢN LÝ HỌC VIÊN
-- =====================================================

CREATE TYPE student_status AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'TRANSFERRED');
CREATE TYPE gender AS ENUM ('MALE', 'FEMALE', 'OTHER');

CREATE TABLE students (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_code VARCHAR(20) UNIQUE NOT NULL, -- HV001, HV002...
    
    -- Thông tin cá nhân
    full_name VARCHAR(100) NOT NULL,
    date_of_birth DATE,
    gender gender,
    id_card VARCHAR(20) UNIQUE,
    
    -- Thông tin liên hệ
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    
    -- Thông tin học tập
    organization_id UUID NOT NULL REFERENCES organizations(id),
    martial_art_id UUID NOT NULL REFERENCES martial_arts(id),
    current_rank_id UUID REFERENCES ranks(id),
    enrollment_date DATE DEFAULT CURRENT_DATE,
    status student_status DEFAULT 'ACTIVE',
    
    -- Thông tin sức khỏe
    health_status TEXT,
    medical_notes TEXT,
    
    -- Ghi chú
    notes TEXT,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID
);

-- =====================================================
-- 3. BẢNG QUẢN LÝ CHUYỂN ĐƠN VỊ
-- =====================================================

CREATE TYPE transfer_status AS ENUM (
    'PENDING', 'FROM_ORG_APPROVED', 'FROM_ORG_REJECTED', 
    'TO_ORG_APPROVED', 'TO_ORG_REJECTED', 
    'PARENT_APPROVED', 'PARENT_REJECTED', 
    'COMPLETED', 'CANCELLED'
);

CREATE TABLE student_transfers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transfer_code VARCHAR(20) UNIQUE NOT NULL,
    
    student_id UUID NOT NULL REFERENCES students(id),
    from_organization_id UUID NOT NULL REFERENCES organizations(id),
    to_organization_id UUID NOT NULL REFERENCES organizations(id),
    
    -- Thông tin yêu cầu
    reason TEXT NOT NULL,
    requested_date DATE DEFAULT CURRENT_DATE,
    expected_transfer_date DATE,
    
    -- Trạng thái duyệt
    status transfer_status DEFAULT 'PENDING',
    
    -- Duyệt cấp 1: Đơn vị cũ
    from_org_approved_at TIMESTAMP,
    from_org_approved_by UUID,
    from_org_notes TEXT,
    
    -- Duyệt cấp 2: Đơn vị mới
    to_org_approved_at TIMESTAMP,
    to_org_approved_by UUID,
    to_org_notes TEXT,
    
    -- Duyệt cấp 3: Cấp trên (nếu cần)
    parent_approved_at TIMESTAMP,
    parent_approved_by UUID,
    parent_notes TEXT,
    
    -- Hoàn thành
    completed_at TIMESTAMP,
    actual_transfer_date DATE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 4. BẢNG QUẢN LÝ THI CỬ VÀ CHỨNG CHỈ
-- =====================================================

CREATE TYPE exam_type AS ENUM ('RANK_PROMOTION', 'CERTIFICATE', 'COMPETITION');
CREATE TYPE exam_status AS ENUM ('SCHEDULED', 'ONGOING', 'COMPLETED', 'CANCELLED');

CREATE TABLE exams (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    exam_code VARCHAR(20) UNIQUE NOT NULL,
    
    organization_id UUID NOT NULL REFERENCES organizations(id),
    martial_art_id UUID NOT NULL REFERENCES martial_arts(id),
    exam_type exam_type NOT NULL,
    
    title VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Thông tin thi
    exam_date DATE NOT NULL,
    exam_time TIME,
    location TEXT,
    max_participants INTEGER,
    registration_deadline DATE,
    
    -- Yêu cầu tham gia
    required_rank_id UUID REFERENCES ranks(id), -- Đai tối thiểu để thi
    target_rank_id UUID REFERENCES ranks(id),   -- Đai sẽ đạt được nếu đỗ
    
    status exam_status DEFAULT 'SCHEDULED',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID
);

-- Đăng ký thi
CREATE TYPE registration_status AS ENUM ('REGISTERED', 'APPROVED', 'REJECTED', 'CANCELLED');

CREATE TABLE exam_registrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    exam_id UUID NOT NULL REFERENCES exams(id),
    student_id UUID NOT NULL REFERENCES students(id),
    
    registration_date DATE DEFAULT CURRENT_DATE,
    status registration_status DEFAULT 'REGISTERED',
    
    -- Phê duyệt
    approved_at TIMESTAMP,
    approved_by UUID,
    approval_notes TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(exam_id, student_id)
);

-- Kết quả thi
CREATE TYPE exam_result AS ENUM ('PASS', 'FAIL', 'ABSENT');

CREATE TABLE exam_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    registration_id UUID NOT NULL REFERENCES exam_registrations(id),

    result exam_result,
    score DECIMAL(5,2), -- Điểm số (0-100)
    notes TEXT,

    -- Thông tin giám khảo
    examiner_name VARCHAR(100),
    examiner_signature TEXT,

    result_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID
);

-- =====================================================
-- 5. BẢNG QUẢN LÝ CHỨNG CHỈ
-- =====================================================

CREATE TYPE certificate_type AS ENUM ('RANK_CERTIFICATE', 'COMPLETION_CERTIFICATE', 'ACHIEVEMENT_CERTIFICATE');
CREATE TYPE certificate_status AS ENUM ('ISSUED', 'REVOKED', 'EXPIRED');

CREATE TABLE certificates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    certificate_code VARCHAR(30) UNIQUE NOT NULL,

    student_id UUID NOT NULL REFERENCES students(id),
    exam_id UUID REFERENCES exams(id),
    martial_art_id UUID NOT NULL REFERENCES martial_arts(id),
    rank_id UUID REFERENCES ranks(id),

    certificate_type certificate_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,

    issue_date DATE DEFAULT CURRENT_DATE,
    expiry_date DATE,
    status certificate_status DEFAULT 'ISSUED',

    -- Chữ ký số
    digital_signature TEXT,
    issuer_name VARCHAR(100),
    issuer_title VARCHAR(100),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID
);

-- =====================================================
-- 6. BẢNG QUẢN LÝ ĐIỂM DANH
-- =====================================================

CREATE TYPE attendance_status AS ENUM ('PRESENT', 'ABSENT', 'LATE', 'EXCUSED');

CREATE TABLE attendance_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_code VARCHAR(20) UNIQUE NOT NULL,

    organization_id UUID NOT NULL REFERENCES organizations(id),
    martial_art_id UUID NOT NULL REFERENCES martial_arts(id),

    session_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME,
    location TEXT,
    instructor_name VARCHAR(100),

    notes TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID
);

CREATE TABLE student_attendance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES attendance_sessions(id),
    student_id UUID NOT NULL REFERENCES students(id),

    status attendance_status NOT NULL,
    check_in_time TIME,
    notes TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,

    UNIQUE(session_id, student_id)
);

-- =====================================================
-- 7. BẢNG QUẢN LÝ TÀI LIỆU HỌC TẬP
-- =====================================================

CREATE TYPE material_type AS ENUM ('VIDEO', 'DOCUMENT', 'IMAGE', 'AUDIO', 'OTHER');
CREATE TYPE material_access AS ENUM ('PUBLIC', 'ORGANIZATION_ONLY', 'RANK_RESTRICTED', 'PRIVATE');

CREATE TABLE learning_materials (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    material_code VARCHAR(20) UNIQUE NOT NULL,

    title VARCHAR(255) NOT NULL,
    description TEXT,
    material_type material_type NOT NULL,

    martial_art_id UUID NOT NULL REFERENCES martial_arts(id),
    required_rank_id UUID REFERENCES ranks(id), -- Đai tối thiểu để xem

    -- File information
    file_path TEXT,
    file_size BIGINT,
    file_format VARCHAR(10),

    access_level material_access DEFAULT 'PUBLIC',

    -- Metadata
    author VARCHAR(100),
    version VARCHAR(10) DEFAULT '1.0',
    tags TEXT[], -- Array of tags

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID
);

-- =====================================================
-- 8. BẢNG QUẢN LÝ TÀI KHOẢN VÀ PHÂN QUYỀN
-- =====================================================

CREATE TYPE user_role AS ENUM (
    'SUPER_ADMIN',      -- Quản trị hệ thống
    'NATIONAL_ADMIN',   -- Quản lý cấp quốc gia
    'CITY_ADMIN',       -- Quản lý cấp tỉnh/thành phố
    'WARD_ADMIN',       -- Quản lý cấp quận/huyện
    'DEPT_ADMIN',       -- Quản lý ban ngành
    'INSTRUCTOR',       -- Huấn luyện viên
    'STUDENT',          -- Học viên
    'VIEWER'            -- Chỉ xem
);

CREATE TYPE user_status AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING');

CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,

    -- Thông tin cá nhân
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),

    -- Phân quyền
    role user_role NOT NULL,
    organization_id UUID REFERENCES organizations(id),
    student_id UUID REFERENCES students(id), -- Nếu là học viên

    status user_status DEFAULT 'PENDING',

    -- Security
    last_login TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,

    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID
);

-- =====================================================
-- 9. INDEXES VÀ CONSTRAINTS
-- =====================================================

-- Indexes for performance
CREATE INDEX idx_organizations_parent ON organizations(parent_id);
CREATE INDEX idx_organizations_level ON organizations(level);
CREATE INDEX idx_students_organization ON students(organization_id);
CREATE INDEX idx_students_martial_art ON students(martial_art_id);
CREATE INDEX idx_students_status ON students(status);
CREATE INDEX idx_transfers_student ON student_transfers(student_id);
CREATE INDEX idx_transfers_status ON student_transfers(status);
CREATE INDEX idx_exams_organization ON exams(organization_id);
CREATE INDEX idx_exams_date ON exams(exam_date);
CREATE INDEX idx_attendance_session ON student_attendance(session_id);
CREATE INDEX idx_attendance_student ON student_attendance(student_id);
CREATE INDEX idx_users_organization ON users(organization_id);
CREATE INDEX idx_users_role ON users(role);

-- Triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_students_updated_at BEFORE UPDATE ON students FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_transfers_updated_at BEFORE UPDATE ON student_transfers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_exams_updated_at BEFORE UPDATE ON exams FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_materials_updated_at BEFORE UPDATE ON learning_materials FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
