# 🥋 HỆ THỐNG QUẢN LÝ HỌC VIÊN VÕ THUẬT 4 CẤP ĐỘ
## Phân tích chi tiết và Roadmap triển khai

---

## 📋 TỔNG QUAN HỆ THỐNG

### 🏗️ Kiến trúc 4 cấp độ quản lý
```
🏛️ QUỐC GIA (Level 1)
├── 🏙️ THÀNH PHỐ/TỈNH (Level 2)
    ├── 🏘️ PHƯỜNG/XÃ (Level 3)
        ├── 🥋 BAN NGÀNH CÙNG CẤP (Level 4)
```

### 🎯 Mục tiêu hệ thống
- Quản lý học viên võ thuật theo cấp bậc hành chính
- Tự động hóa quy trình đăng ký, chuyển đơn vị, thi cử
- Cấp chứng nhận và quản lý đẳng cấp
- <PERSON><PERSON><PERSON> cáo và thống kê toàn diện
- Hỗ trợ đa nền tảng: Web Admin, Mobile App, Public Portal

---

## 🦀 BACKEND RUST ARCHITECTURE

### 🔧 Tech Stack
```toml
# Core Framework
axum = "0.7"                    # Modern async web framework
tokio = "1.0"                   # Async runtime
tower = "0.4"                   # Service abstractions

# Database & ORM
sqlx = "0.7"                    # Async SQL toolkit
sea-orm = "0.12"                # Modern ORM
redis = "0.24"                  # Cache layer

# Security & Auth
jsonwebtoken = "9.0"            # JWT handling
argon2 = "0.5"                  # Password hashing
oauth2 = "4.4"                  # OAuth2 integration

# Serialization & Validation
serde = "1.0"                   # JSON serialization
validator = "0.16"              # Input validation
chrono = "0.4"                  # Date/time handling

# Monitoring & Logging
tracing = "0.1"                 # Structured logging
prometheus = "0.13"             # Metrics collection

# Message Queue
lapin = "2.3"                   # RabbitMQ client
rdkafka = "0.36"               # Apache Kafka client

# File Storage
aws-sdk-s3 = "1.0"             # S3 integration
```

### 🏗️ Microservices Architecture
1. **API Gateway Service** - Request routing, authentication, rate limiting
2. **Auth Service** - JWT token management, role-based access control
3. **Organization Service** - Hierarchy management, registration workflow
4. **Student Service** - CRUD operations, profile management, search
5. **Exam Service** - Exam scheduling, registration, result processing
6. **Certificate Service** - Certificate generation, digital signatures
7. **Transfer Service** - Multi-level approval workflow, state machine
8. **Attendance Service** - Real-time check-in, bulk operations
9. **Material Service** - File management, access control
10. **Report Service** - Data aggregation, analytics, export
11. **Notification Service** - Email/SMS, push notifications

---

## 🗄️ DATABASE DESIGN

### 📊 Core Entities
- **ORGANIZATION** - Đơn vị quản lý 4 cấp
- **USER** - Tài khoản hệ thống với roles
- **STUDENT** - Thông tin học viên
- **RANK** - Đẳng cấp võ thuật
- **EXAM** - Kỳ thi và đăng ký
- **CERTIFICATE** - Chứng chỉ và chứng nhận
- **TRANSFER_REQUEST** - Yêu cầu chuyển đơn vị
- **ATTENDANCE** - Điểm danh học viên
- **LEARNING_MATERIAL** - Tài liệu học tập
- **STUDENT_HISTORY** - Lịch sử hoạt động

### 🔗 Key Relationships
- Organization: Self-referencing hierarchy (parent_org_id)
- Student: Belongs to Organization, has current Rank
- Transfer: Multi-level approval workflow
- Exam: Linked to Rank, managed by Organization
- Certificate: Issued for Student after passing Exam

---

## 🔄 QUY TRÌNH CHÍNH

### 1. Đăng ký Đơn vị Quản lý
```
Đơn vị mới → Xác định cấp độ → Nộp hồ sơ → Cấp trên duyệt → Cấp mã đơn vị → Kích hoạt
```

### 2. Chuyển đơn vị Học viên (2 cấp duyệt)
```
Học viên đề xuất → Đơn vị cũ duyệt → Đơn vị mới duyệt → Cấp trên duyệt (nếu khác cấp) → Thực hiện chuyển
```

### 3. Tổ chức Kỳ thi
```
Đề xuất tổ chức → Phê duyệt → Đăng ký thí sinh → Kiểm tra điều kiện → Thi → Chấm điểm → Cấp chứng nhận
```

### 4. Quản lý Đẳng cấp
```
Học viên đạt yêu cầu → Đăng ký thi → Thi đạt → Cấp chứng nhận → Cập nhật đẳng cấp → Ghi nhận lịch sử
```

---

## 🎨 GIAO DIỆN NGƯỜI DÙNG

### 🖥️ Web Admin Portal
**Phân quyền theo cấp độ:**
- **Admin Quốc Gia**: Dashboard tổng quan, quản lý tỉnh/thành phố, tiêu chuẩn đẳng cấp
- **Admin Tỉnh/Thành phố**: Quản lý phường/xã, tổ chức thi cấp tỉnh
- **Admin Phường/Xã**: Quản lý ban ngành, thi địa phương
- **Admin Ban Ngành**: Quản lý học viên, điểm danh, đăng ký thi

**Menu chính:**
- 🏠 Dashboard & Thống kê
- 🏢 Quản lý Tổ chức
- 👥 Quản lý Học viên
- 📝 Quản lý Kỳ thi
- 🏅 Chứng chỉ
- 🔄 Chuyển đơn vị
- 📅 Điểm danh
- 📚 Tài liệu
- 📊 Báo cáo
- ⚙️ Hệ thống

### 📱 Mobile App
**Dành cho Quản lý:**
- Dashboard di động
- Điểm danh QR Code/NFC
- Thông báo real-time
- Phê duyệt nhanh

**Dành cho Học viên:**
- Hồ sơ cá nhân
- Lịch sử thi và chứng chỉ
- Đăng ký thi
- Yêu cầu chuyển đơn vị

### 🌐 Public Portal
- Tra cứu chứng chỉ
- Xác thực chứng chỉ
- Danh sách tổ chức
- Lịch thi công khai

---

## 🚀 ROADMAP TRIỂN KHAI 12 THÁNG

### 📅 Phase 1: Foundation (Tháng 1-2)
**Mục tiêu:** Thiết lập nền tảng và core services
- ✅ Database design & setup
- ✅ Rust project structure & CI/CD
- 🔄 Authentication Service (JWT + OAuth2)
- 🔄 Organization Service (hierarchy management)
- 🔄 Basic API Gateway

**Deliverables:**
- PostgreSQL database với full schema
- Auth service với JWT và role-based access
- Organization CRUD với hierarchy support
- Docker containerization
- Basic CI/CD pipeline

### 📅 Phase 2: Core Features (Tháng 3-4)
**Mục tiêu:** Phát triển tính năng cốt lõi
- 🔄 Student Management Service
- 🔄 Basic Web Admin Portal (React + TypeScript)
- 🔄 Exam Management Service
- 🔄 Certificate Service

**Deliverables:**
- Student CRUD với search và filter
- Web admin với authentication
- Exam scheduling và registration
- Certificate generation với digital signature
- Redis caching layer

### 📅 Phase 3: Advanced Features (Tháng 5-6)
**Mục tiêu:** Tính năng nâng cao và workflow
- 🔄 Transfer Service với multi-level approval
- 🔄 Attendance Service với real-time support
- 🔄 Mobile App Development (React Native/Flutter)
- 🔄 Notification Service (Email/SMS/Push)

**Deliverables:**
- Transfer workflow với state machine
- Attendance với QR code/NFC
- Mobile app MVP
- Notification system với templates
- Message queue integration (RabbitMQ)

### 📅 Phase 4: Enhancement (Tháng 7-8)
**Mục tiêu:** Báo cáo, phân tích và tối ưu
- 🔄 Report & Analytics Service
- 🔄 Material Management Service
- 🔄 Public Portal
- 🔄 Advanced search với Elasticsearch

**Deliverables:**
- Dashboard với charts và analytics
- File management với S3 integration
- Public portal cho tra cứu
- Full-text search capability
- Performance optimization

### 📅 Phase 5: Testing & Deployment (Tháng 9-10)
**Mục tiêu:** Testing toàn diện và deployment
- 🔄 Integration testing
- 🔄 Performance testing & optimization
- 🔄 Security audit
- 🔄 Production deployment

**Deliverables:**
- Comprehensive test suite
- Load testing results
- Security penetration testing
- Production-ready deployment
- Monitoring và alerting setup

### 📅 Phase 6: Launch & Support (Tháng 11-12)
**Mục tiêu:** Go-live và hỗ trợ
- 🔄 User training và documentation
- 🔄 Go-live support
- 🔄 Bug fixes và improvements
- 🔄 Feature enhancements

**Deliverables:**
- User training materials
- Production support procedures
- Bug tracking và resolution
- Performance monitoring
- User feedback integration

---

## 👥 NHÂN LỰC VÀ CHI PHÍ

### 🏗️ Team Structure
- **Project Manager**: 1 người (full-time)
- **Rust Backend Developers**: 3 người (full-time)
- **Frontend Developers**: 2 người (React/TypeScript)
- **Mobile Developers**: 2 người (React Native/Flutter)
- **UI/UX Designer**: 1 người (part-time)
- **DevOps Engineer**: 1 người (part-time)
- **QA Engineers**: 2 người (full-time)

### 💰 Ước tính Chi phí (12 tháng)
- **Nhân lực**: $250,000 - $350,000
- **Infrastructure**: $25,000 - $35,000
- **Third-party Services**: $15,000 - $20,000
- **Tools & Licenses**: $8,000 - $12,000
- **Contingency (10%)**: $30,000 - $42,000
- **TỔNG CỘNG**: $328,000 - $459,000

---

## 🔒 BẢO MẬT VÀ TUÂN THỦ

### 🛡️ Security Measures
- **Authentication**: JWT với refresh token rotation
- **Authorization**: Role-based access control (RBAC)
- **Data Encryption**: TLS 1.3, AES-256 for data at rest
- **Input Validation**: Comprehensive validation với Rust validator
- **Rate Limiting**: Per-user và per-endpoint limits
- **Audit Logging**: Comprehensive audit trail
- **Security Headers**: CORS, CSP, HSTS implementation

### 📋 Compliance
- **Data Privacy**: GDPR-compliant data handling
- **Audit Trail**: Complete activity logging
- **Backup & Recovery**: Automated backup với point-in-time recovery
- **Disaster Recovery**: Multi-region deployment capability

---

## 📊 MONITORING VÀ PERFORMANCE

### 📈 Key Metrics
- **Response Time**: < 200ms cho 95% requests
- **Throughput**: 1000+ requests/second
- **Availability**: 99.9% uptime
- **Database Performance**: Query optimization với indexing
- **Memory Usage**: Rust's zero-cost abstractions
- **Error Rate**: < 0.1% error rate

### 🔍 Monitoring Stack
- **Application**: Prometheus + Grafana
- **Logging**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **Tracing**: Jaeger distributed tracing
- **Health Checks**: Automated health monitoring
- **Alerting**: PagerDuty integration

---

## 🔧 CHI TIẾT KỸ THUẬT RUST

### 🏗️ Project Structure
```
martial-arts-system/
├── Cargo.toml
├── docker-compose.yml
├── services/
│   ├── api-gateway/          # API Gateway service
│   ├── auth-service/         # Authentication service
│   ├── organization-service/ # Organization management
│   ├── student-service/      # Student management
│   ├── exam-service/         # Exam management
│   ├── certificate-service/  # Certificate management
│   ├── transfer-service/     # Transfer workflow
│   ├── attendance-service/   # Attendance tracking
│   ├── material-service/     # Material management
│   ├── report-service/       # Reporting & analytics
│   └── notification-service/ # Notifications
├── shared/
│   ├── database/            # Database models & migrations
│   ├── auth/               # Shared auth utilities
│   ├── config/             # Configuration management
│   └── utils/              # Common utilities
├── migrations/             # Database migrations
├── tests/                 # Integration tests
└── docs/                  # API documentation
```

### 🗄️ Database Schema (PostgreSQL)
```sql
-- Core tables với Rust-friendly types
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    level INTEGER NOT NULL CHECK (level BETWEEN 1 AND 4),
    parent_id UUID REFERENCES organizations(id),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    manager_name VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

CREATE TABLE students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    full_name VARCHAR(100) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    cccd VARCHAR(20) UNIQUE,
    birth_date DATE,
    gender VARCHAR(10) CHECK (gender IN ('Male', 'Female', 'Other')),
    org_id UUID NOT NULL REFERENCES organizations(id),
    current_rank_id UUID REFERENCES ranks(id),
    enrolled_at TIMESTAMPTZ DEFAULT NOW(),
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE
);

CREATE TABLE transfer_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL REFERENCES students(id),
    from_org_id UUID NOT NULL REFERENCES organizations(id),
    to_org_id UUID NOT NULL REFERENCES organizations(id),
    request_date TIMESTAMPTZ DEFAULT NOW(),
    reason TEXT,
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (
        status IN ('PENDING', 'APPROVED_BY_FROM', 'APPROVED_BY_TO',
                  'APPROVED_BY_PARENT', 'COMPLETED', 'REJECTED')
    ),
    approved_by_from UUID REFERENCES users(id),
    approved_by_to UUID REFERENCES users(id),
    approved_by_parent UUID REFERENCES users(id),
    approved_date TIMESTAMPTZ,
    notes TEXT
);
```

### 🔌 API Examples với Axum
```rust
// Student Service - main.rs
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    routing::{get, post, put, delete},
    Router,
};
use serde::{Deserialize, Serialize};
use sqlx::PgPool;
use uuid::Uuid;

#[derive(Serialize, Deserialize)]
struct Student {
    id: Uuid,
    full_name: String,
    email: Option<String>,
    phone: Option<String>,
    org_id: Uuid,
    current_rank_id: Option<Uuid>,
    is_active: bool,
}

#[derive(Deserialize)]
struct CreateStudentRequest {
    full_name: String,
    email: Option<String>,
    phone: Option<String>,
    org_id: Uuid,
    address: Option<String>,
    cccd: Option<String>,
}

async fn create_student(
    State(pool): State<PgPool>,
    Json(payload): Json<CreateStudentRequest>,
) -> Result<Json<Student>, StatusCode> {
    let student = sqlx::query_as!(
        Student,
        r#"
        INSERT INTO students (full_name, email, phone, org_id, address, cccd)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, full_name, email, phone, org_id, current_rank_id, is_active
        "#,
        payload.full_name,
        payload.email,
        payload.phone,
        payload.org_id,
        payload.address,
        payload.cccd
    )
    .fetch_one(&pool)
    .await
    .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(Json(student))
}

async fn get_students(
    State(pool): State<PgPool>,
    Query(params): Query<HashMap<String, String>>,
) -> Result<Json<Vec<Student>>, StatusCode> {
    let org_id: Option<Uuid> = params.get("org_id")
        .and_then(|s| s.parse().ok());

    let students = sqlx::query_as!(
        Student,
        r#"
        SELECT id, full_name, email, phone, org_id, current_rank_id, is_active
        FROM students
        WHERE ($1::uuid IS NULL OR org_id = $1) AND is_active = true
        ORDER BY full_name
        "#,
        org_id
    )
    .fetch_all(&pool)
    .await
    .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(Json(students))
}

fn create_router() -> Router<PgPool> {
    Router::new()
        .route("/students", post(create_student))
        .route("/students", get(get_students))
        .route("/students/:id", get(get_student_by_id))
        .route("/students/:id", put(update_student))
        .route("/students/:id", delete(delete_student))
        .route("/students/:id/history", get(get_student_history))
        .route("/students/search", get(search_students))
}
```

### 🔐 Authentication với JWT
```rust
// auth-service/src/lib.rs
use jsonwebtoken::{decode, encode, DecodingKey, EncodingKey, Header, Validation};
use serde::{Deserialize, Serialize};
use chrono::{Duration, Utc};

#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,        // User ID
    org_id: String,     // Organization ID
    role: String,       // User role
    permissions: Vec<String>, // User permissions
    exp: i64,          // Expiration time
    iat: i64,          // Issued at
}

#[derive(Debug, Serialize, Deserialize)]
struct TokenResponse {
    access_token: String,
    refresh_token: String,
    token_type: String,
    expires_in: i64,
}

pub async fn generate_tokens(
    user_id: Uuid,
    org_id: Uuid,
    role: String,
    permissions: Vec<String>,
) -> Result<TokenResponse, AuthError> {
    let now = Utc::now();
    let access_exp = now + Duration::hours(1);
    let refresh_exp = now + Duration::days(30);

    let access_claims = Claims {
        sub: user_id.to_string(),
        org_id: org_id.to_string(),
        role: role.clone(),
        permissions: permissions.clone(),
        exp: access_exp.timestamp(),
        iat: now.timestamp(),
    };

    let refresh_claims = Claims {
        sub: user_id.to_string(),
        org_id: org_id.to_string(),
        role,
        permissions,
        exp: refresh_exp.timestamp(),
        iat: now.timestamp(),
    };

    let access_token = encode(
        &Header::default(),
        &access_claims,
        &EncodingKey::from_secret(JWT_SECRET.as_ref()),
    )?;

    let refresh_token = encode(
        &Header::default(),
        &refresh_claims,
        &EncodingKey::from_secret(REFRESH_SECRET.as_ref()),
    )?;

    Ok(TokenResponse {
        access_token,
        refresh_token,
        token_type: "Bearer".to_string(),
        expires_in: 3600,
    })
}
```

### 🔄 Transfer Service với State Machine
```rust
// transfer-service/src/workflow.rs
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TransferStatus {
    Pending,
    ApprovedByFrom,
    ApprovedByTo,
    ApprovedByParent,
    Completed,
    Rejected,
}

#[derive(Debug, Clone)]
pub struct TransferRequest {
    pub id: Uuid,
    pub student_id: Uuid,
    pub from_org_id: Uuid,
    pub to_org_id: Uuid,
    pub status: TransferStatus,
    pub approved_by_from: Option<Uuid>,
    pub approved_by_to: Option<Uuid>,
    pub approved_by_parent: Option<Uuid>,
}

impl TransferRequest {
    pub fn can_approve_by_from(&self) -> bool {
        matches!(self.status, TransferStatus::Pending)
    }

    pub fn can_approve_by_to(&self) -> bool {
        matches!(self.status, TransferStatus::ApprovedByFrom)
    }

    pub fn can_approve_by_parent(&self) -> bool {
        matches!(self.status, TransferStatus::ApprovedByTo)
    }

    pub async fn approve_by_from(
        &mut self,
        approver_id: Uuid,
        pool: &PgPool,
    ) -> Result<(), TransferError> {
        if !self.can_approve_by_from() {
            return Err(TransferError::InvalidStatusTransition);
        }

        self.status = TransferStatus::ApprovedByFrom;
        self.approved_by_from = Some(approver_id);

        self.save_to_db(pool).await?;
        self.send_notification().await?;

        Ok(())
    }

    pub async fn approve_by_to(
        &mut self,
        approver_id: Uuid,
        pool: &PgPool,
    ) -> Result<(), TransferError> {
        if !self.can_approve_by_to() {
            return Err(TransferError::InvalidStatusTransition);
        }

        // Check if parent approval is needed
        let needs_parent_approval = self.needs_parent_approval(pool).await?;

        if needs_parent_approval {
            self.status = TransferStatus::ApprovedByTo;
        } else {
            self.status = TransferStatus::Completed;
            self.execute_transfer(pool).await?;
        }

        self.approved_by_to = Some(approver_id);
        self.save_to_db(pool).await?;

        Ok(())
    }

    async fn needs_parent_approval(&self, pool: &PgPool) -> Result<bool, TransferError> {
        // Logic to determine if parent approval is needed
        // Based on organization hierarchy levels
        let from_org = get_organization(self.from_org_id, pool).await?;
        let to_org = get_organization(self.to_org_id, pool).await?;

        // Need parent approval if transferring between different level 2 orgs
        Ok(from_org.get_level_2_parent() != to_org.get_level_2_parent())
    }
}
```

---

## 🎯 KẾT LUẬN VÀ KHUYẾN NGHỊ

### ✅ Ưu điểm của Rust Backend
- **Performance**: Hiệu suất cao, zero-cost abstractions, memory safety
- **Concurrency**: Excellent async support với Tokio runtime
- **Type Safety**: Compile-time error detection, prevents runtime errors
- **Ecosystem**: Rich crate ecosystem với mature libraries
- **Scalability**: Excellent for microservices, low resource usage
- **Reliability**: Rust's ownership system prevents common bugs

### 🚀 Khuyến nghị Triển khai
1. **MVP First**: Tập trung vào core features (Auth, Organization, Student)
2. **Incremental Deployment**: Deploy từng service một cách độc lập
3. **Database First**: Thiết kế database schema trước khi code
4. **Testing Strategy**: Unit tests + Integration tests + Load tests
5. **Monitoring Early**: Thiết lập tracing và metrics từ đầu
6. **Documentation**: Comprehensive API documentation với OpenAPI
7. **Error Handling**: Robust error handling với custom error types

### 📋 Rust-Specific Recommendations
1. **Use SeaORM**: Modern ORM với compile-time query checking
2. **Async All The Way**: Full async/await pattern với Tokio
3. **Error Handling**: Use `thiserror` và `anyhow` crates
4. **Configuration**: Use `config` crate với environment variables
5. **Logging**: Structured logging với `tracing` crate
6. **Testing**: Use `tokio-test` cho async tests

### 🔄 Development Workflow
1. **Setup**: Cargo workspace với shared dependencies
2. **Database**: SQLx migrations với compile-time query checking
3. **API Design**: OpenAPI spec first, then implement
4. **Testing**: TDD approach với comprehensive test coverage
5. **CI/CD**: GitHub Actions với Rust-specific optimizations
6. **Deployment**: Docker containers với multi-stage builds

### 📋 Next Steps
1. **Environment Setup**: Rust toolchain, PostgreSQL, Redis
2. **Project Initialization**: Cargo workspace setup
3. **Database Design**: Finalize schema và migrations
4. **Core Services**: Start với Auth và Organization services
5. **API Gateway**: Implement routing và middleware
6. **Frontend Integration**: API contracts và documentation

---

## 🌐 10. KẾ HOẠCH TRIỂN KHAI WEBSITE (12 THÁNG)

### 📅 **TIMELINE TỔNG QUAN**

```
Phase 1: Foundation (Tháng 1-3)
├── UI/UX Design System (6 tuần)
├── Next.js Setup & Architecture (4 tuần)
└── Authentication Pages (4 tuần)

Phase 2: Admin Portal (Tháng 4-7)
├── Organization Management (6 tuần)
├── Student Management (6 tuần)
└── Exam Management (8 tuần)

Phase 3: Core Features (Tháng 8-10)
├── Transfer Management (6 tuần)
├── Certificate Management (6 tuần)
└── Attendance System (4 tuần)

Phase 4: Advanced Features (Tháng 11-12)
├── Reporting Dashboard (4 tuần)
└── Material Management (4 tuần)

Phase 5: Testing & Launch (Tháng 13-15)
├── Integration Testing (6 tuần)
├── Performance Optimization (4 tuần)
└── Production Deployment (4 tuần)
```

### 🎨 **PHASE 1: FOUNDATION (Tháng 1-3)**

#### 🎨 **UI/UX Design System (6 tuần)**
```typescript
// Design System Components:
- Color palette (martial arts themed)
- Typography system
- Component library
- Icon set
- Layout grids
- Responsive breakpoints
- Accessibility guidelines

// Key Design Principles:
- Clean and professional
- Martial arts aesthetic
- Hierarchical information display
- Mobile-first approach
- Accessibility (WCAG 2.1 AA)
- Multi-language support

// Component Library:
interface DesignSystem {
  colors: {
    primary: '#1976d2',      // Blue for trust
    secondary: '#dc004e',    // Red for martial arts
    success: '#388e3c',      // Green for success
    warning: '#f57c00',      // Orange for warnings
    error: '#d32f2f',        // Red for errors
    neutral: '#424242'       // Gray for text
  },
  typography: {
    h1: 'Roboto, 32px, 700',
    h2: 'Roboto, 28px, 600',
    h3: 'Roboto, 24px, 600',
    body1: 'Roboto, 16px, 400',
    body2: 'Roboto, 14px, 400',
    caption: 'Roboto, 12px, 400'
  },
  spacing: [4, 8, 16, 24, 32, 48, 64],
  breakpoints: {
    xs: '0px',
    sm: '600px',
    md: '960px',
    lg: '1280px',
    xl: '1920px'
  }
}
```

#### ⚡ **Next.js Setup & Architecture (4 tuần)**
```typescript
// Tech Stack:
- Next.js 14 (App Router)
- TypeScript
- Tailwind CSS
- Shadcn/ui components
- React Query (TanStack Query)
- Zustand for state management
- React Hook Form + Zod validation

// Project Structure:
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication routes
│   ├── (dashboard)/       # Protected dashboard routes
│   ├── (public)/          # Public routes
│   └── api/               # API routes (if needed)
├── components/            # Reusable components
│   ├── ui/               # Base UI components
│   ├── forms/            # Form components
│   ├── charts/           # Chart components
│   └── layout/           # Layout components
├── lib/                  # Utilities and configurations
│   ├── api.ts           # API client
│   ├── auth.ts          # Authentication logic
│   ├── utils.ts         # Utility functions
│   └── validations.ts   # Zod schemas
├── hooks/               # Custom React hooks
├── stores/              # Zustand stores
└── types/               # TypeScript type definitions

// Key Features:
- Server-side rendering (SSR)
- Static site generation (SSG) where appropriate
- API route optimization
- Image optimization
- Bundle optimization
- SEO optimization
```

#### 🔐 **Authentication Pages (4 tuần)**
```typescript
// Authentication Flow:
interface AuthPages {
  '/login': LoginPage,
  '/register': RegisterPage,
  '/forgot-password': ForgotPasswordPage,
  '/reset-password': ResetPasswordPage,
  '/verify-email': VerifyEmailPage,
  '/profile': ProfilePage
}

// Login Component:
'use client'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { loginSchema } from '@/lib/validations'

export function LoginForm() {
  const form = useForm({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
      remember: false
    }
  })

  const { mutate: login, isLoading } = useMutation({
    mutationFn: authApi.login,
    onSuccess: (data) => {
      // Store tokens
      // Redirect to dashboard
      // Show success message
    },
    onError: (error) => {
      // Show error message
      // Handle specific error cases
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(login)}>
        <FormField name="username" />
        <FormField name="password" />
        <FormField name="remember" />
        <Button type="submit" loading={isLoading}>
          Đăng nhập
        </Button>
      </form>
    </Form>
  )
}

// Features:
- Multi-factor authentication UI
- Social login integration
- Password strength indicator
- Account lockout handling
- Session management
- Role-based redirects
```

### 📋 **PHASE 2: ADMIN PORTAL (Tháng 4-7)**

#### 🏢 **Organization Management (6 tuần)**
```typescript
// Organization Management Features:
- 4-level hierarchy visualization
- Organization registration workflow
- Document upload and management
- Approval process tracking
- Organization search and filtering
- Bulk operations

// Key Components:
interface OrganizationManagement {
  OrganizationTree: React.FC<{
    organizations: Organization[]
    onSelect: (org: Organization) => void
    onExpand: (orgId: string) => void
  }>

  OrganizationForm: React.FC<{
    organization?: Organization
    mode: 'create' | 'edit'
    onSubmit: (data: OrganizationFormData) => void
  }>

  ApprovalWorkflow: React.FC<{
    organizationId: string
    currentStep: ApprovalStep
    onApprove: (decision: ApprovalDecision) => void
  }>
}

// Organization Tree Component:
export function OrganizationTree({ organizations, onSelect }: OrganizationTreeProps) {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())

  const renderNode = (org: Organization, level: number) => (
    <div className={`ml-${level * 4} border-l-2 border-gray-200`}>
      <div className="flex items-center p-2 hover:bg-gray-50">
        <Badge variant={getLevelVariant(org.level)}>
          {getLevelLabel(org.level)}
        </Badge>
        <span className="ml-2 font-medium">{org.name}</span>
        <span className="ml-auto text-sm text-gray-500">
          {org.studentsCount} học viên
        </span>
      </div>
      {expandedNodes.has(org.id) && org.children?.map(child =>
        renderNode(child, level + 1)
      )}
    </div>
  )

  return (
    <div className="space-y-1">
      {organizations.map(org => renderNode(org, 0))}
    </div>
  )
}

// Features:
- Drag-and-drop hierarchy management
- Real-time updates via WebSocket
- Advanced filtering and search
- Export functionality
- Audit trail visualization
```

#### 👥 **Student Management (6 tuần)**
```typescript
// Student Management Features:
- Student registration and profile management
- Advanced search and filtering
- Bulk operations (import/export)
- Student history tracking
- Photo management
- Document management

// Key Components:
interface StudentManagement {
  StudentList: React.FC<{
    filters: StudentFilters
    onFilterChange: (filters: StudentFilters) => void
  }>

  StudentProfile: React.FC<{
    studentId: string
    onUpdate: (data: StudentUpdateData) => void
  }>

  StudentForm: React.FC<{
    student?: Student
    mode: 'create' | 'edit'
    onSubmit: (data: StudentFormData) => void
  }>
}

// Student List with Advanced Filtering:
export function StudentList() {
  const [filters, setFilters] = useState<StudentFilters>({
    search: '',
    organization: '',
    martialArt: '',
    rank: '',
    status: 'ACTIVE',
    ageRange: [0, 100],
    enrollmentDateRange: [null, null]
  })

  const { data: students, isLoading } = useQuery({
    queryKey: ['students', filters],
    queryFn: () => studentApi.getStudents(filters),
    keepPreviousData: true
  })

  return (
    <div className="space-y-4">
      <StudentFilters filters={filters} onChange={setFilters} />

      <DataTable
        columns={studentColumns}
        data={students?.data || []}
        loading={isLoading}
        pagination={students?.pagination}
        onRowClick={(student) => router.push(`/students/${student.id}`)}
      />
    </div>
  )
}

// Student Profile with Tabs:
export function StudentProfile({ studentId }: { studentId: string }) {
  const { data: student } = useQuery({
    queryKey: ['student', studentId],
    queryFn: () => studentApi.getStudent(studentId)
  })

  return (
    <Tabs defaultValue="profile" className="w-full">
      <TabsList>
        <TabsTrigger value="profile">Thông tin cá nhân</TabsTrigger>
        <TabsTrigger value="academic">Học tập</TabsTrigger>
        <TabsTrigger value="exams">Kỳ thi</TabsTrigger>
        <TabsTrigger value="certificates">Chứng chỉ</TabsTrigger>
        <TabsTrigger value="transfers">Chuyển đơn vị</TabsTrigger>
        <TabsTrigger value="attendance">Điểm danh</TabsTrigger>
      </TabsList>

      <TabsContent value="profile">
        <StudentProfileForm student={student} />
      </TabsContent>

      <TabsContent value="academic">
        <StudentAcademicHistory studentId={studentId} />
      </TabsContent>

      {/* Other tabs... */}
    </Tabs>
  )
}

// Features:
- Photo cropping and upload
- QR code generation for student ID
- Bulk import from Excel/CSV
- Advanced search with multiple criteria
- Student timeline visualization
```

---

**📅 Ngày tạo:** 2024-12-19
**👤 Người phân tích:** Augment Agent
**🦀 Backend:** Rust + Axum + SeaORM + PostgreSQL
**🔄 Phiên bản:** 2.0 (Updated with Rust details)
