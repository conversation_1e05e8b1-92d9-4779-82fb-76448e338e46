#!/bin/bash

echo "📦 ĐANG KIỂM TRA PHẦN MỀM CÀI TRÊN MACOS..."

# 1. Ứng dụng trong /Applications
echo ""
echo "🖥 Ứng dụng trong /Applications:"
num_apps=$(ls /Applications | wc -l)
ls /Applications
echo "🔢 Tổng số: $num_apps ứng dụng"

# 2. <PERSON><PERSON><PERSON> mềm cài qua Homebrew (nếu có)
if command -v brew &> /dev/null; then
  echo ""
  echo "🍺 Gói cài qua Homebrew:"
  num_brews=$(brew list | wc -l)
  brew list
  echo "🔢 Tổng số: $num_brews gói"
else
  echo ""
  echo "⚠️ Homebrew chưa được cài đặt."
fi

# 3. <PERSON><PERSON><PERSON> hệ thống cài bằng Apple Installer (.pkg)
echo ""
echo "📦 Gói cài đặt hệ thống (qua pkgutil):"
num_pkgs=$(pkgutil --pkgs | wc -l)
pkgutil --pkgs
echo "🔢 Tổng số: $num_pkgs gói"

echo ""
echo "✅ KIỂM TRA HOÀN TẤT!"
