#!/bin/bash

echo "🧨 GỠ CÀI ĐẶT MONGODB TRÊN MACOS..."

# 1. Dừng dịch vụ MongoDB nếu có
echo "⛔️ Dừng MongoDB (nếu đang chạy)..."
brew services stop mongodb-community 2>/dev/null
brew services stop mongodb 2>/dev/null
pkill mongod 2>/dev/null

# 2. Gỡ cài đặt từ Homebrew (nếu có)
echo "🧹 Gỡ MongoDB khỏi Homebrew (nếu dùng)..."
brew uninstall mongodb-community 2>/dev/null
brew uninstall mongodb/brew/mongodb-community 2>/dev/null
brew uninstall mongodb 2>/dev/null
brew cleanup 2>/dev/null

# 3. Xoá dữ liệu và cấu hình
echo "🧹 Xoá dữ liệu và cấu hình MongoDB..."
sudo rm -rf /usr/local/var/mongodb
sudo rm -rf ~/data/db
sudo rm -rf /data/db
sudo rm -rf /usr/local/etc/mongod.conf
sudo rm -rf /usr/local/var/log/mongodb
sudo rm -rf /var/log/mongodb
sudo rm -rf ~/.mongodb
sudo rm -rf ~/.config/mongodb

# 4. Xoá binary nếu cài thủ công (not via Homebrew)
echo "🗑 Xoá các binary nếu cài thủ công..."
sudo rm -f /usr/local/bin/mongo
sudo rm -f /usr/local/bin/mongod
sudo rm -f /usr/local/bin/mongos

# 5. Xoá ứng dụng MongoDB Compass (GUI)
echo "🗑 Xoá MongoDB Compass (nếu có)..."
sudo rm -rf /Applications/MongoDB\ Compass*.app

echo "✅ MongoDB đã được gỡ hoàn toàn khỏi hệ thống!"
