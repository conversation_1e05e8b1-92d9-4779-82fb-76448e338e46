# 🌐 KẾ HOẠCH TRIỂN KHAI WEBSITE - HỆ THỐNG QUẢN LÝ HỌC VIÊN VÕ THUẬT

## 📋 **TỔNG QUAN DỰ ÁN**

### 🎯 **Mục tiêu Website**
- **Admin Portal**: <PERSON>u<PERSON>n lý toàn diện hệ thống 4 cấp độ
- **Public Portal**: <PERSON>ra cứu chứng chỉ, thông tin tổ chức
- **Mobile-Responsive**: T<PERSON><PERSON> ưu cho mọi thiết bị
- **Real-time Features**: Cập nhật trực tiếp, thông báo
- **Multi-language**: Hỗ trợ đa ngôn ngữ

### 🛠️ **Tech Stack**
```typescript
Frontend:
- Next.js 14 (App Router)
- TypeScript
- Tailwind CSS + Shadcn/ui
- React Query (TanStack Query)
- Zustand (State Management)
- React Hook Form + Zod

Backend Integration:
- Rust API Services
- WebSocket connections
- JWT Authentication
- File upload (S3)
- Real-time notifications
```

### 📅 **Timeline: 12 Tháng**
```
Phase 1: Foundation (Tháng 1-3)
Phase 2: Admin Portal (Tháng 4-7)
Phase 3: Core Features (Tháng 8-10)
Phase 4: Advanced Features (Tháng 11-12)
Phase 5: Testing & Launch (Tháng 13-15)
```

---

## 🎨 **PHASE 1: FOUNDATION (Tháng 1-3)**

### **Week 1-6: UI/UX Design System**

#### **Design Principles**
- **Martial Arts Aesthetic**: Màu sắc mạnh mẽ, typography rõ ràng
- **Hierarchical Display**: Thể hiện rõ cấu trúc 4 cấp độ
- **Accessibility**: WCAG 2.1 AA compliance
- **Mobile-First**: Responsive design cho mọi thiết bị

#### **Color Palette**
```css
:root {
  --primary: #1976d2;      /* Blue - Trust & Authority */
  --secondary: #dc004e;    /* Red - Martial Arts Spirit */
  --success: #388e3c;      /* Green - Success States */
  --warning: #f57c00;      /* Orange - Warnings */
  --error: #d32f2f;        /* Red - Errors */
  --neutral: #424242;      /* Gray - Text */
  --background: #fafafa;   /* Light Gray - Background */
  --surface: #ffffff;      /* White - Cards */
}
```

#### **Typography System**
```css
/* Roboto Font Family */
.text-h1 { font: 700 32px/40px Roboto; }
.text-h2 { font: 600 28px/36px Roboto; }
.text-h3 { font: 600 24px/32px Roboto; }
.text-body1 { font: 400 16px/24px Roboto; }
.text-body2 { font: 400 14px/20px Roboto; }
.text-caption { font: 400 12px/16px Roboto; }
```

#### **Component Library**
- **Buttons**: Primary, Secondary, Outline, Ghost variants
- **Forms**: Input, Select, Checkbox, Radio, DatePicker
- **Navigation**: Sidebar, Breadcrumb, Tabs, Pagination
- **Data Display**: Table, Card, Badge, Avatar, Progress
- **Feedback**: Alert, Toast, Modal, Loading states
- **Layout**: Container, Grid, Stack, Divider

### **Week 7-10: Next.js Architecture Setup**

#### **Project Structure**
```
martial-arts-website/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/            # Auth routes group
│   │   │   ├── login/
│   │   │   ├── register/
│   │   │   └── forgot-password/
│   │   ├── (dashboard)/       # Protected routes
│   │   │   ├── organizations/
│   │   │   ├── students/
│   │   │   ├── exams/
│   │   │   ├── certificates/
│   │   │   ├── transfers/
│   │   │   ├── attendance/
│   │   │   ├── materials/
│   │   │   └── reports/
│   │   ├── (public)/          # Public routes
│   │   │   ├── verify-certificate/
│   │   │   ├── organizations/
│   │   │   └── exam-schedule/
│   │   ├── api/               # API routes (if needed)
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── components/            # Reusable components
│   │   ├── ui/               # Base UI components
│   │   ├── forms/            # Form components
│   │   ├── charts/           # Chart components
│   │   ├── layout/           # Layout components
│   │   └── features/         # Feature-specific components
│   ├── lib/                  # Utilities
│   │   ├── api.ts           # API client
│   │   ├── auth.ts          # Auth utilities
│   │   ├── utils.ts         # General utilities
│   │   ├── validations.ts   # Zod schemas
│   │   └── constants.ts     # App constants
│   ├── hooks/               # Custom React hooks
│   ├── stores/              # Zustand stores
│   ├── types/               # TypeScript definitions
│   └── styles/              # Additional styles
├── public/                  # Static assets
├── package.json
├── tailwind.config.js
├── next.config.js
└── tsconfig.json
```

#### **Core Configuration**
```typescript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['your-s3-bucket.s3.amazonaws.com'],
  },
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_WS_URL: process.env.NEXT_PUBLIC_WS_URL,
  },
}

module.exports = nextConfig

// tailwind.config.js
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: '#1976d2',
        secondary: '#dc004e',
        // ... other colors
      },
      fontFamily: {
        sans: ['Roboto', 'sans-serif'],
      },
    },
  },
  plugins: [require('@tailwindcss/forms')],
}
```

### **Week 11-14: Authentication System**

#### **Auth Flow Implementation**
```typescript
// lib/auth.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      
      login: async (credentials) => {
        const response = await authApi.login(credentials)
        set({
          user: response.user,
          token: response.access_token,
          isAuthenticated: true,
        })
      },
      
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        })
      },
      
      refreshToken: async () => {
        const { token } = get()
        if (!token) return
        
        const response = await authApi.refreshToken(token)
        set({ token: response.access_token })
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
```

#### **Login Page**
```typescript
// app/(auth)/login/page.tsx
'use client'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useAuthStore } from '@/lib/auth'

const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  remember: z.boolean().default(false),
})

type LoginForm = z.infer<typeof loginSchema>

export default function LoginPage() {
  const login = useAuthStore((state) => state.login)
  
  const form = useForm<LoginForm>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
      remember: false,
    },
  })

  const onSubmit = async (data: LoginForm) => {
    try {
      await login(data)
      router.push('/dashboard')
    } catch (error) {
      toast.error('Login failed. Please check your credentials.')
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Đăng nhập hệ thống
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={form.handleSubmit(onSubmit)}>
          <div>
            <Input
              {...form.register('username')}
              placeholder="Tên đăng nhập"
              error={form.formState.errors.username?.message}
            />
          </div>
          <div>
            <Input
              {...form.register('password')}
              type="password"
              placeholder="Mật khẩu"
              error={form.formState.errors.password?.message}
            />
          </div>
          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                {...form.register('remember')}
                type="checkbox"
                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-900">Ghi nhớ đăng nhập</span>
            </label>
            <Link href="/forgot-password" className="text-sm text-primary hover:underline">
              Quên mật khẩu?
            </Link>
          </div>
          <Button
            type="submit"
            className="w-full"
            loading={form.formState.isSubmitting}
          >
            Đăng nhập
          </Button>
        </form>
      </div>
    </div>
  )
}
```

---

## 📋 **PHASE 2: ADMIN PORTAL (Tháng 4-7)**

### **Week 15-20: Organization Management**

#### **Organization Tree Component**
```typescript
// components/features/organizations/OrganizationTree.tsx
interface Organization {
  id: string
  name: string
  level: 1 | 2 | 3 | 4
  parentId?: string
  children?: Organization[]
  studentsCount: number
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING'
}

export function OrganizationTree({ organizations, onSelect }: OrganizationTreeProps) {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())
  
  const toggleNode = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes)
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId)
    } else {
      newExpanded.add(nodeId)
    }
    setExpandedNodes(newExpanded)
  }
  
  const renderNode = (org: Organization, level: number = 0) => (
    <div key={org.id} className={`ml-${level * 4}`}>
      <div className="flex items-center p-2 hover:bg-gray-50 rounded-lg cursor-pointer">
        {org.children && org.children.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => toggleNode(org.id)}
            className="p-1 h-6 w-6"
          >
            {expandedNodes.has(org.id) ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
        )}
        
        <Badge variant={getLevelVariant(org.level)} className="ml-2">
          {getLevelLabel(org.level)}
        </Badge>
        
        <span className="ml-2 font-medium text-gray-900">{org.name}</span>
        
        <Badge variant="outline" className="ml-auto">
          {org.studentsCount} học viên
        </Badge>
        
        <Badge 
          variant={org.status === 'ACTIVE' ? 'success' : 'secondary'}
          className="ml-2"
        >
          {org.status}
        </Badge>
      </div>
      
      {expandedNodes.has(org.id) && org.children?.map(child => 
        renderNode(child, level + 1)
      )}
    </div>
  )
  
  return (
    <div className="space-y-1">
      {organizations.map(org => renderNode(org))}
    </div>
  )
}

const getLevelVariant = (level: number) => {
  switch (level) {
    case 1: return 'destructive'  // National - Red
    case 2: return 'default'      // City - Blue
    case 3: return 'secondary'    // Ward - Gray
    case 4: return 'outline'      // Department - Outline
    default: return 'outline'
  }
}

const getLevelLabel = (level: number) => {
  switch (level) {
    case 1: return 'Quốc gia'
    case 2: return 'Tỉnh/TP'
    case 3: return 'Phường/Xã'
    case 4: return 'Ban ngành'
    default: return 'Unknown'
  }
}
```

#### **Organization Form**
```typescript
// components/features/organizations/OrganizationForm.tsx
const organizationSchema = z.object({
  name: z.string().min(1, 'Tên tổ chức là bắt buộc'),
  level: z.number().min(1).max(4),
  parentId: z.string().optional(),
  address: z.string().min(1, 'Địa chỉ là bắt buộc'),
  phone: z.string().regex(/^[0-9+\-\s()]+$/, 'Số điện thoại không hợp lệ'),
  email: z.string().email('Email không hợp lệ'),
  managerName: z.string().min(1, 'Tên người quản lý là bắt buộc'),
  description: z.string().optional(),
})

export function OrganizationForm({ organization, mode, onSubmit }: OrganizationFormProps) {
  const form = useForm<OrganizationFormData>({
    resolver: zodResolver(organizationSchema),
    defaultValues: organization || {
      name: '',
      level: 4,
      address: '',
      phone: '',
      email: '',
      managerName: '',
      description: '',
    },
  })

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tên tổ chức *</FormLabel>
                <FormControl>
                  <Input placeholder="Nhập tên tổ chức" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="level"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Cấp độ *</FormLabel>
                <Select onValueChange={(value) => field.onChange(parseInt(value))}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn cấp độ" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="1">Quốc gia</SelectItem>
                    <SelectItem value="2">Tỉnh/Thành phố</SelectItem>
                    <SelectItem value="3">Phường/Xã</SelectItem>
                    <SelectItem value="4">Ban ngành</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Địa chỉ *</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Nhập địa chỉ đầy đủ"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Số điện thoại *</FormLabel>
                <FormControl>
                  <Input placeholder="0123456789" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email *</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="managerName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tên người quản lý *</FormLabel>
              <FormControl>
                <Input placeholder="Nhập tên người quản lý" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Hủy
          </Button>
          <Button type="submit" loading={form.formState.isSubmitting}>
            {mode === 'create' ? 'Tạo mới' : 'Cập nhật'}
          </Button>
        </div>
      </form>
    </Form>
  )
}
```

---

*File này chứa kế hoạch chi tiết triển khai website. Để xem đầy đủ, vui lòng mở file `martial_arts_system_analysis.md` trong workspace.*
